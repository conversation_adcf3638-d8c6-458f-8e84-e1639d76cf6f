import { Injectable } from '@angular/core';
import { CartItem } from '../model/cart-item.model';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CartService {
  private items: CartItem[] = [];
  private itemsSubject = new BehaviorSubject<CartItem[]>([]);

  constructor() {
    this.loadCartFromStorage();
  }

  private isLocalStorageAvailable(): boolean {
    try {
      return typeof localStorage !== 'undefined' && localStorage !== null;
    } catch {
      return false;
    }
  }

  private loadCartFromStorage() {
    if (!this.isLocalStorageAvailable()) {
      console.warn('localStorage n’est pas disponible.');
      this.items = [];
      this.itemsSubject.next(this.items);
      return;
    }

    try {
      const data = localStorage.getItem('cart');
      if (data) {
        this.items = JSON.parse(data);
        if (!Array.isArray(this.items)) {
          // Si ce n'est pas un tableau, on réinitialise
          this.items = [];
        }
      } else {
        this.items = [];
      }
    } catch (error) {
      console.error('Erreur lors du chargement du panier depuis localStorage:', error);
      this.items = [];
    }
    this.itemsSubject.next(this.items);
  }

  private saveCartToStorage() {
    if (!this.isLocalStorageAvailable()) {
      console.warn('localStorage n’est pas disponible, impossible de sauvegarder le panier.');
      return;
    }
    localStorage.setItem('cart', JSON.stringify(this.items));
  }

  getCart() {
    return this.itemsSubject.asObservable();
  }

  addToCart(item: CartItem) {
    const existing = this.items.find(i => i.productId === item.productId);
    if (existing) {
      existing.quantity += item.quantity;
    } else {
      this.items.push(item);
    }
    this.saveCartToStorage();
    this.itemsSubject.next(this.items);
  }

  removeItem(productId: string) {
    this.items = this.items.filter(i => i.productId !== productId);
    this.saveCartToStorage();
    this.itemsSubject.next(this.items);
  }

  clearCart() {
    this.items = [];
    this.saveCartToStorage();
    this.itemsSubject.next(this.items);
  }

  updateCart(): void {
    this.saveCartToStorage();
    this.itemsSubject.next(this.items);
  }

  getTotalAmount(): number {
    return this.items.reduce((total, item) => total + item.quantity * item.prix, 0);
  }
}
