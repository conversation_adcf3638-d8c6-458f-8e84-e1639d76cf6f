
<!-- Container principal avec design moderne -->
<div class="bg-gray-50 min-h-screen p-6">
  
  <!-- Header de la section -->
  <div class="mb-8">
    <h1 class="text-4xl font-bold text-gray-800 mb-2 flex items-center">
      <i class="fas fa-store mr-4 text-blue-600"></i>
      Notre Catalogue
    </h1>
    <p class="text-gray-600 text-lg">Découvrez tous nos produits disponibles</p>
    
    <!-- Stats rapides -->
    <div class="mt-4 flex items-center space-x-6 text-sm">
      <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">
        <i class="fas fa-box mr-1"></i>
        {{ produits.length || 0 }} produits
      </span>
      <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">
        <i class="fas fa-check-circle mr-1"></i>
        En stock
      </span>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="!produits" class="flex items-center justify-center py-16">
    <div class="text-center">
      <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p class="text-gray-600 text-lg">Chargement des produits...</p>
    </div>
  </div>

  <!-- Grille de produits -->
  <div *ngIf="produits && produits.length > 0" 
       class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
    
<div *ngFor="let produit of produits; let i = index" 
     (click)="handleClick(produit)"
     class="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden group transform hover:-translate-y-2 cursor-pointer">

      <!-- Image Container -->
     <div class="relative h-64 overflow-hidden">
  <img *ngIf="produit.imageUrl && produit.imageUrl.length > 0"
       [src]="produit.imageUrl[0]" 
       [alt]="produit.nom" 
       class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
  <div *ngIf="!produit.imageUrl || produit.imageUrl.length === 0"
       class="w-full h-full flex items-center justify-center bg-gray-200 text-gray-500">
    Pas d'image
  </div>
        
        <!-- Overlay gradient -->
        <div class="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-0 group-hover:opacity-60 transition-opacity duration-300"></div>
        
        <!-- Stock badge -->
        <div class="absolute top-3 left-3">
          <span [ngClass]="{
                  'bg-green-500': produit.stock > 10,
                  'bg-orange-500': produit.stock > 0 && produit.stock <= 10,
                  'bg-red-500': produit.stock === 0
                }" 
                class="text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
            <i class="fas fa-boxes mr-1"></i>
            <span *ngIf="produit.stock > 0"> en stock</span>
            <span *ngIf="produit.stock === 0">Rupture</span>
          </span>
        </div>

        <!-- Prix promotion badge -->
        <div *ngIf="produit.prixPromotion && produit.prixPromotion < produit.prix" 
             class="absolute top-3 right-3 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg animate-pulse">
          <i class="fas fa-fire mr-1"></i>
          -{{ ((produit.prix - produit.prixPromotion) / produit.prix * 100).toFixed(0) }}%
        </div>

        <!-- Quick actions overlay -->
        <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="flex space-x-3">
            <button (click)="viewDetails(produit)" 
                    class="bg-white text-gray-800 p-3 rounded-full shadow-lg hover:bg-gray-100 transition-colors duration-200">
              <i class="fas fa-eye"></i>
            </button>
            <button (click)="addToFavorites(produit)" 
                    class="bg-white text-red-500 p-3 rounded-full shadow-lg hover:bg-red-50 transition-colors duration-200">
              <i class="fas fa-heart"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Content -->
      <div class="p-6">
        <!-- Category -->
        <div class="mb-3">
          <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-wide">
            {{ produit.categorie }}
          </span>
        </div>

        <!-- Title -->
        <h3 class="text-xl font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors line-clamp-2">
          {{ produit.nom }}
        </h3>

        <!-- Description -->
        <p class="text-gray-600 text-sm mb-4 line-clamp-3">
          {{ produit.caracteristiques }}
        </p>

        <!-- Tags section -->
        <div class="mb-4 space-y-2">
          <!-- Couleurs -->
          <div *ngIf="produit.couleur && produit.couleur.length > 0" class="flex items-center">
            <i class="fas fa-palette text-pink-500 mr-2 text-xs"></i>
            <div class="flex flex-wrap gap-1">
              <span *ngFor="let couleur of produit.couleur.slice(0, 3)" 
                    class="bg-pink-100 text-pink-800 px-2 py-1 rounded-full text-xs">
                {{ couleur }}
              </span>
              <span *ngIf="produit.couleur.length > 3" 
                    class="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                +{{ produit.couleur.length - 3 }}
              </span>
            </div>
          </div>

          <!-- Tailles -->
          <div *ngIf="produit.taille && produit.taille.length > 0" class="flex items-center">
            <i class="fas fa-ruler text-blue-500 mr-2 text-xs"></i>
            <div class="flex flex-wrap gap-1">
              <span *ngFor="let taille of produit.taille.slice(0, 4)" 
                    class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-mono">
                {{ taille }}
              </span>
              <span *ngIf="produit.taille.length > 4" 
                    class="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                +{{ produit.taille.length - 4 }}
              </span>
            </div>
          </div>
        </div>

        <!-- Prix section -->
        <div class="mb-4">
          <div class="flex items-center justify-between">
            <div>
              <!-- Prix promotion -->
              <div *ngIf="produit.prixPromotion && produit.prixPromotion < produit.prix" class="space-y-1">
                <p class="text-2xl font-bold text-green-600">
                  {{ produit.prixPromotion | currency:'EUR':'symbol':'1.2-2':'fr-FR' }}
                </p>
                <p class="text-lg text-gray-500 line-through">
                  {{ produit.prix | currency:'EUR':'symbol':'1.2-2':'fr-FR' }}
                </p>
              </div>
              
              <!-- Prix normal -->
              <p *ngIf="!(produit.prixPromotion && produit.prixPromotion < produit.prix)" 
                 class="text-2xl font-bold text-blue-600">
                {{ produit.prix | currency:'EUR':'symbol':'1.2-2':'fr-FR' }}
              </p>
            </div>

            <!-- Rating stars (simulation) -->
            <div class="flex items-center">
              <div class="flex text-yellow-400 mr-1">
                <i class="fas fa-star" *ngFor="let star of [1,2,3,4,5]"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Action buttons -->
        <div class="flex gap-3">
          <button (click)="addToCart(produit)" 
                  [disabled]="produit.stock === 0"
                  class="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold py-3 px-4 rounded-xl shadow-lg hover:shadow-xl disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100">
            <i class="fas fa-shopping-cart mr-2"></i>
            <span *ngIf="produit.stock > 0">Ajouter</span>
            <span *ngIf="produit.stock === 0">Indisponible</span>
          </button>
          
        <button (click)="viewDetails(produit)" 
  class="bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-2 px-3 text-sm rounded-xl transition-colors duration-200 flex items-center space-x-1">
  <i class="fas fa-info-circle text-sm"></i>
  <span>Voir plus</span>
</button>

        </div>
      </div>
    </div>
  </div>

  <!-- Empty state -->
  <div *ngIf="produits && produits.length === 0" 
       class="text-center py-16">
    <div class="bg-gray-200 rounded-full w-32 h-32 flex items-center justify-center mx-auto mb-6">
      <i class="fas fa-box-open text-6xl text-gray-400"></i>
    </div>
    <h3 class="text-2xl font-semibold text-gray-600 mb-2">Aucun produit disponible</h3>
    <p class="text-gray-500">Revenez bientôt pour découvrir nos nouveautés !</p>
  </div>
</div>