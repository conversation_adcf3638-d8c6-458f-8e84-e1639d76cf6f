import { ActivatedRoute, Router } from '@angular/router';
import { CartItem } from '../model/cart-item.model';
import { Product } from '../model/produit.model';
import { CartService } from '../services/cart.service';
import { ProduitService } from '../services/produit.service';
import { Component, OnInit } from '@angular/core';
import { FavoriteService } from '../services/favorite.service';
import { FavoriteItem } from '../model/favorite-item.model';
import { ToastNotificationService } from '../services/toast-notification.service';

@Component({
  selector: 'app-product-list',
  templateUrl: './product-list.component.html',
  styleUrl: './product-list.component.scss'
})
export class ProductListComponent implements OnInit {
 produits: Product[] = [];
constructor(
  private produitService: ProduitService,
  private cartService: CartService,
  private route: ActivatedRoute,
  private router: Router,
      private favoriteService: FavoriteService,
                private toastService: ToastNotificationService
      
      
) {}


  ngOnInit() {
    this.produitService.getProduits().subscribe(produits => {
      console.log(produits);
      this.produits = produits;
    });
}

addToCart(produit: Product) {
  const item: CartItem = {
    productId: produit.idp!,
    nom: produit.nom,
    prix: produit.prix,
    quantity: 1,
    images: produit.imageUrl
  };
  this.cartService.addToCart(item);
this.toastService.showSuccess(
        'Ajouté au panier ! 🛒',
        `${produit.nom} a été ajouté à votre panier avec succès`
      );  }

viewDetails(produit: Product) {
  this.router.navigate(['/produit', produit.idp]);
}

  addToFavorites(produit: Product) {
    const prix = Number(produit.prixPromotion ?? produit.prix ?? 0);
  const item: FavoriteItem = {
    productId: produit.idp!,
    nom: produit.nom,
        quantity: 1, // quantité initiale

      images: produit.imageUrl,
    prix: prix

  };
  this.favoriteService.addToFavorites(item);
this.toastService.showSuccess(
          '${produit.nom} ',
          'a été ajouter aux favoriss✅'
        );
      }
   private showNotification(message: string, type: 'success' | 'error' | 'info') {
    // Vous pouvez utiliser une library comme ngx-toastr ou créer votre propre système
    alert(message); // Remplacez par votre système de notification
  }

// Pour les étoiles (propriété du composant)
Math = Math;

clickTimeout: any = null;
clickDelay = 250; // temps max entre deux clics pour être considéré comme un double clic

handleClick(produit: any): void {
  if (this.clickTimeout) {
    clearTimeout(this.clickTimeout);
    this.clickTimeout = null;
    this.addToFavorites(produit); // double clic
  } else {
    this.clickTimeout = setTimeout(() => {
      this.viewDetails(produit); // simple clic
      this.clickTimeout = null;
    }, this.clickDelay);
  }
}


}
