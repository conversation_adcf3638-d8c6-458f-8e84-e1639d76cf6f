import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { from, Observable } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { RegisterRequest } from '../model/RegisterRequest.model';
import { AuthResponse } from '../model/AuthResponse.model';
import { AuthRequest } from '../model/AuthRequest.model';
import { User } from '../model/user.model';
import { Router } from '@angular/router';
import { isPlatformBrowser } from '@angular/common';
import { JwtHelperService } from '@auth0/angular-jwt';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private baseUrl = 'http://localhost:8081/api/auth'; // API de ton backend
  public token!: string;
  public roles!: string;
  public loggedUser!: string;
  public isloggedIn = false;
  constructor(private http: HttpClient, private afAuth: AngularFireAuth,private router: Router,@Inject(PLATFORM_ID) private platformId: Object,  private helper: JwtHelperService) {}

  loginWithEmail(email: string, password: string): Observable<AuthResponse> {
    return from(this.afAuth.signInWithEmailAndPassword(email, password)).pipe(
      switchMap(userCredential => {
        const user = userCredential.user;
        if (!user) throw new Error('Utilisateur Firebase introuvable');

              localStorage.setItem('uid', user.uid);


        // On récupère le Firebase ID Token (à envoyer au backend)
        return from(user.getIdToken(true));
      }),
      switchMap(firebaseToken => {
        const body: AuthRequest = { firebaseToken };
        return this.http.post<AuthResponse>(`${this.baseUrl}/login`, body);
      })
    );
  }

  register(registerRequest: RegisterRequest): Observable<AuthResponse> { // ✅ retourne AuthResponse
    return this.http.post<AuthResponse>(`${this.baseUrl}/register`, registerRequest);
  }
  updatePassword(uid: string, oldPassword: string, newPassword: string): Observable<any> {
    const url = `${this.baseUrl}/update-password?uid=${uid}&oldPassword=${oldPassword}&newPassword=${newPassword}`;


    // Ajouter responseType pour spécifier que la réponse est en texte brut
    return this.http.put(url, {}, { responseType: 'text' });

  }
  getCurrentUser(): any {
    if (isPlatformBrowser(this.platformId)) {
      const userJson = localStorage.getItem('role');
      return userJson ? JSON.parse(userJson) : null;
    }
    return null;
  }
  logout() {
    this.afAuth.signOut().then(() => {
      localStorage.removeItem('jwt');
      localStorage.removeItem('role'); // Optionnel : nettoyer les données locales
      // Optionnel : nettoyer les données locales
      this.router.navigate(['/home']); // Rediriger vers la page d'accueil
    });
  }
  saveToken(jwt: string) {
    localStorage.setItem('jwt', jwt);
    this.token = jwt;
    this.isloggedIn = true;
    this.decodeJWT();
  }


  decodeJWT() {
    if (!this.token) return;
    const decodedToken = this.helper.decodeToken(this.token);
    this.roles = decodedToken.role; // ou decodedToken.roles si tu changes côté backend
    this.loggedUser = decodedToken.sub;
  }

  loadToken(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.token = localStorage.getItem('jwt') || '';
      const role = localStorage.getItem('role');
      const user = localStorage.getItem('username');
      const email = localStorage.getItem('email');
      const photoURL = localStorage.getItem('photoURL');
      const phone = localStorage.getItem('phoneNumber');
      const address = localStorage.getItem('address');

      if (role) this.roles = role;
      if (user) this.loggedUser = user;
      this.isloggedIn = !!this.token;


      // Optionnel : décode automatiquement si token chargé
      if (this.token) this.decodeJWT();
    }
  }


  getToken(): string {
    return this.token;
  }

  isTokenExpired(): boolean {
    return this.helper.isTokenExpired(this.token);
  }

  isAdmin(): boolean {
    return this.roles === 'ADMIN';
  }

  isUser(): boolean {
    return this.roles === 'USER';
  }

}
