import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { OrderService, OrderStatus } from '../services/order.service';
import { Order } from '../model/order.model';
import { UserService } from '../services/user.service';
import { ToastNotificationService } from '../services/toast-notification.service';



@Component({
    selector: 'app-orders',
    templateUrl: './orders.component.html',
    styleUrls: ['./orders.component.scss']
})
export class OrdersComponent implements OnInit {

allStatuses = Object.values(OrderStatus);
  orders: Order[] = [];
   userMap: { [key: string]: string } = {}; // Maps userId → userName
  searchTerm: string = '';
  isAdmin: boolean = false;


  constructor(private router: Router, private orderService: OrderService , private userService: UserService,        private toastService: ToastNotificationService
  ) {}

  ngOnInit(): void {
    this.isAdmin = localStorage.getItem('role') === 'ADMIN';
    this.loadOrders();

    if (this.isAdmin) {
      this.orderService.getAllOrders().subscribe({
        next: (orders) => {
          this.orders = orders;
          this.fetchUserNames(orders);
        },
        error: (err) => console.error('Error fetching orders:', err)
      });
    }
  }

  fetchUserNames(orders: Order[]) {
    const uniqueUserIds = [...new Set(orders.map(order => order.userId))];

    uniqueUserIds.forEach(userId => {
      this.userService.getUserById(userId).subscribe({
        next: user => this.userMap[userId] = user.displayName,
        error: err => {
          console.warn(`User not found for ID ${userId}`);
          this.userMap[userId] = 'Utilisateur inconnu';
        }
      });
    });
  }

  getUserName(userId: string): string {
    return this.userMap[userId] || 'Chargement...';
  }

  get filteredOrders() {
    return this.orders.filter(order =>
      this.getUserName(order.userId).toLowerCase().includes(this.searchTerm.toLowerCase()) &&
      order.status !== 'ARCHIVED'
    );
  }
  archiveOrder(order: Order) {
    order.status = 'ARCHIVED';
    // Optionally send a request to backend to persist the update
  }

  viewOrder(order: Order) {
    this.router.navigate(['/commande-detail', order.id]);
  }

  goToDashboard() {
    this.router.navigate(['/dashboard']);
  }
 orderStatuses = Object.values(OrderStatus);

 loadOrders() {
    this.orderService.getAllOrders().subscribe({
      next: (orders) => {
        this.orders = orders;
        // Debug: Afficher les statuts reçus de la base
        console.log('Statuts reçus de la base:', orders.map(o => ({ id: o.id, status: o.status })));
      },
      error: (err) => {
        console.error('Erreur lors du chargement des commandes', err);
      }
    });
  }

  onStatusChange(orderId: string, newStatus: string) {
    console.log('Changement de statut:', { orderId, newStatus });
    
    const status = newStatus as OrderStatus;
    
    this.orderService.updateOrderStatus(orderId, status).subscribe({
      next: (updatedOrder) => {
        console.log('Statut mis à jour:', updatedOrder);
        // Mise à jour locale
        const order = this.orders.find(o => o.id === orderId);
        if (order) {
          order.status = updatedOrder.status;
        }
      },
      error: (err) => {
        console.error('Erreur lors de la mise à jour du statut', err);
        alert('Impossible de mettre à jour le statut');
      }
    });
  }
  getStatusClass(status: string): string {
  switch (status) {
    case 'EN_ATTENTE': return 'status-pending';
    case 'CONFIRMÉE': return 'status-confirmed';
    case 'EN_PRÉPARATION': return 'status-preparing';
    case 'EXPÉDIÉE': return 'status-shipped';
    case 'LIVRÉE': return 'status-delivered';
    case 'ANNULÉE': return 'status-canceled';
    default: return '';
  }
}
// Ajoutez ces méthodes à votre OrdersComponent

getOrdersByStatus(status: OrderStatus): Order[] {
  return this.filteredOrders.filter(order => order.status === status);
}

getStatusLabel(status: OrderStatus): string {
  switch (status) {
    case OrderStatus.EN_ATTENTE: return 'En attente';
    case OrderStatus.CONFIRMEE: return 'Confirmée';
    case OrderStatus.EN_PREPARATION: return 'En préparation';
    case OrderStatus.EXPEDIEE: return 'Expédiée';
    case OrderStatus.LIVREE: return 'Livrée';
    case OrderStatus.ANNULEE: return 'Annulée';
    case OrderStatus.ARCHIVED: return 'Archivée';
    default: return status;
  }
}
}


