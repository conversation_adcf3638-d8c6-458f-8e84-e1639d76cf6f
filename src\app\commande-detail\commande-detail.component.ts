import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { OrderService } from '../services/order.service';
import { Order } from '../model/order.model';

@Component({
    selector: 'app-commande-detail',
    templateUrl: './commande-detail.component.html',
    styleUrls: ['./commande-detail.component.scss']
})
export class CommandeDetailComponent implements OnInit {
    order: Order | null = null;
    loading = true;
    error = '';
    showAlert = false;
    alertMessage = '';
    alertType = 'warning'; // 'success', 'warning', 'error', 'info'

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private orderService: OrderService
    ) {
        const token = localStorage.getItem('jwt');

        if (!token) {
            this.showCustomAlert(
                "🔐 Authentification requise ! Veuillez vous connecter pour consulter les détails de votre commande.",
                'warning'
            );
            return;
        }
    }

    ngOnInit(): void {
        const orderId = this.route.snapshot.paramMap.get('id');
        if (orderId) {
            this.orderService.getOrderById(orderId).subscribe({
                next: (order) => {
                    console.log('Order full data:', order);
                    this.order = order;
                    this.loading = false;
                },
                error: (err) => {
                    this.error = 'Erreur lors du chargement de la commande';
                    this.loading = false;
                    this.showCustomAlert(
                        "❌ Oops ! Une erreur s'est produite lors du chargement de votre commande.",
                        'error'
                    );
                }
            });
        }
    }

    showCustomAlert(message: string, type: string = 'info'): void {
        this.alertMessage = message;
        this.alertType = type;
        this.showAlert = true;
    }

    closeAlert(): void {
        this.showAlert = false;
        if (this.alertType === 'warning' && this.alertMessage.includes('Authentification')) {
            // Rediriger après fermeture de l'alerte d'authentification
            setTimeout(() => {
                this.router.navigate(['/']);
            }, 300);
        }
    }

    goBack(): void {
        window.history.back();
    }
}