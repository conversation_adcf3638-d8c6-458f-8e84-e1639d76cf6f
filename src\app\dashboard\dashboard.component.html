<div *ngIf="isAdmin; else accessDenied" class="dashboard-container">
  <h1>Tableau de Bord</h1>

  <div class="sections-container">
    <div *ngFor="let section of sections" class="section-card">
      <a [routerLink]="section.route">
        <img [src]="section.image" alt="{{ section.title }}">
        <h3>{{ section.title }}</h3>
      </a>
    </div>
  </div>
</div>

<ng-template #accessDenied>
  <div class="dashboard-container">
    <h1>Accès refusé</h1>
    <p>Cette page est réservée aux administrateurs.</p>
  </div>
</ng-template>
