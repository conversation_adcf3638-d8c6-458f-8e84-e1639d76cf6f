.avatar-container {
  max-width: 400px;
  margin: 0 auto;
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  text-align: center;

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937; // gray-800
    margin-bottom: 1rem;
  }

  .camera-wrapper {
    position: relative;
    width: 100%;
    height: 360px;
    border-radius: 0.75rem;
    overflow: hidden;
    margin-bottom: 1rem;

    iframe {
      position: absolute;
      top: -300px; // Ajuste selon ton cadrage dans Unreal
      left: -650px;
      width: 1920px;
      height: 1080px;
      border: none;
    }
  }

  button {
    background-color: #4f46e5; // indigo-600
    color: white;
    padding: 0.5rem 1.25rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: background-color 0.3s ease;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);

    &:hover {
      background-color: #4338ca; // indigo-700
    }
  }

  p {
    margin-top: 0.75rem;
    font-size: 0.875rem;
    color: #4b5563; // gray-600
  }
}
