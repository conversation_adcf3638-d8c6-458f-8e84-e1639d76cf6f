import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';

import { Observable } from 'rxjs';
import { Order } from '../model/order.model';
export enum OrderStatus {
  EN_ATTENTE = 'EN_ATTENTE',
  CONFIRMEE = 'CONFIRMÉE',
  EN_PREPARATION = 'EN_PRÉPARATION',
  EXPEDIEE = 'EXPÉDIÉE',
  LIVREE = 'LIVRÉE',
  ANNULEE = 'ANNULÉE',
  ARCHIVED = "ARCHIVED"
}
@Injectable({
  providedIn: 'root'
})
export class OrderService {
  
  private apiUrl = 'http://localhost:8082/api/orders';

  constructor(private http: HttpClient) {}

  getAllOrders(): Observable<Order[]> {
    return this.http.get<Order[]>(this.apiUrl);
  }

  getOrderById(id: string): Observable<Order> {
    return this.http.get<Order>(`${this.apiUrl}/${id}`);
  }

  addOrder(order: Order): Observable<string> {
    return this.http.post(this.apiUrl, order, { responseType: 'text' });
  }

  updateOrder(id: string, order: Order): Observable<string> {
    return this.http.put(`${this.apiUrl}/${id}`, order, { responseType: 'text' });
  }

  deleteOrder(id: string): Observable<string> {
    return this.http.delete(`${this.apiUrl}/${id}`, { responseType: 'text' });
  }
getOrdersByUser(userId: string): Observable<any[]> {
  return this.http.get<any[]>(`http://localhost:8082/api/orders/user/${userId}`);
}


createOrderAfterPayment(order: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/create-after-payment`, order);
  }

 updateOrderStatus(orderId: string, status: OrderStatus): Observable<Order> {
  const params = new HttpParams().set('status', status);
  return this.http.put<Order>(
    `http://localhost:8082/api/orders/${orderId}/status`,
    null,  // corps vide
    { params }
  );
}

}
