.profile-container {
  margin: 20px;
  font-family: Arial, sans-serif;
}

.profile-card {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  margin: 0 auto;
}

.profile-card h1 {
  margin-bottom: 15px;
  font-size: 2rem; /* Taille de police plus grande */
  font-weight: bold; /* Texte en gras */
  color: transparent; /* Couleur de texte transparente */
  background: linear-gradient(90deg, #168ddc, #10052b); /* Dégradé de couleur */
  -webkit-background-clip: text; /* Applique le dégradé au texte */
  background-clip: text;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2); /* Ombre portée */
}

.profile-card h2 {
  margin-bottom: 10px;
  color: black; /* Titre en noir */
}

.profile-card p {
  margin: 10px 0;
  color: black; /* Texte des données en noir */
}

.profile-card strong {
  color: #007bff; /* Texte des labels en bleu */
}

.edit-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 20px;
  display: block;
  width: 100%;
  text-align: center;
}

.edit-btn:hover {
  background-color: #2980b9;
}

/* Formulaire de modification des coordonnées */
form {
  margin-top: 20px;
}

label {
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
  display: inline-block;
}

input[type="text"],
input[type="password"] {
  width: 100%;
  padding: 10px;
  margin: 8px 0;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff; /* Fond blanc */
  color: #000; /* Texte en noir */
}

input[type="text"]:focus,
input[type="password"]:focus {
  outline: none;
  border-color: #007bff; /* Bordure bleue lors du focus */
}

input[type="text"]::placeholder,
input[type="password"]::placeholder {
  color: #aaa; /* Texte de l'placeholder en gris clair */
}

/* Styles pour les boutons */
.form-buttons {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

button[type="submit"],
button[type="button"] {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  text-align: center;
  display: inline-block;
  width: 48%;
}

button[type="submit"]:hover,
button[type="button"]:hover {
  background-color: #2980b9;
}

button[type="submit"] {
  background-color: #2980b9;
}

button[type="button"] {
  background-color: #c82333;
}

button[type="submit"]:hover {
  background-color: #2980b9;
}

button[type="button"]:hover {
  background-color: #c82333;
}

button[type="submit"]:active,
button[type="button"]:active {
  transform: scale(0.98);
}

/* Styles supplémentaires pour les boutons */
.password-buttons {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

/* Modifier mot de passe */
.password-buttons button {
  width: 48%;
  background-color: #f39c12;
  color: white;
  border-radius: 5px;
}

.password-buttons button:hover {
  background-color: #e67e22;
}

.password-buttons button:active {
  transform: scale(0.98);
}

.password-buttons .cancel-btn {
  background-color: #e74c3c;
}

.password-buttons .cancel-btn:hover {
  background-color: #c0392b;
}

