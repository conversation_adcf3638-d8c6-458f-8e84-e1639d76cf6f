
import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';


@Injectable({
  providedIn: 'root'
})
export class SpeechService {

  private baseUrl = 'http://localhost:8083/api/speech'; // adapte si nécessaire


  constructor(private http: HttpClient) { }

  /**
   * Lance le streaming audio depuis le microphone (backend).
   */

  startStreaming(): Observable<string> {
    const sessionId = localStorage.getItem('uid') || '';

    // Ajouter sessionId dans un header personnalisé, par exemple 'X-Session-Id'
    const headers = new HttpHeaders({
      'X-Session-Id': sessionId
    });

    return this.http.get<string>(`${this.baseUrl}/stream`, { headers, responseType: 'text' as 'json' })
      .pipe(catchError(this.handleError));
  }
  /**
   * Arrête le streaming audio (backend).
   */
 stopStreaming(): Observable<string> {
  return this.http.post(`${this.baseUrl}/stop`, {}, { responseType: 'text' })
    .pipe(catchError(this.handleError));
}


  private handleError(error: HttpErrorResponse) {
    let errorMessage = '';
    if (error.error instanceof ErrorEvent) {
      errorMessage = `Une erreur s'est produite : ${error.error.message}`;
    } else {
      errorMessage = `Le serveur a renvoyé le code ${error.status}, message : ${error.message}`;
    }
    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }


}
