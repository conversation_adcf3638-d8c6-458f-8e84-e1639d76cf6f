import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { AuthModalService } from '../model/auth-modal.service'; // adapte le chemin

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(
    private authModalService: AuthModalService,
    private router: Router
  ) {}

  canActivate(): boolean {
    const jwt = localStorage.getItem('jwt');  // ta logique de connexion
    if (!jwt) {
      this.authModalService.openModal();  // Ouvre le modal

      this.router.navigate(['/home']);    // Redirige vers home (optionnel)
      return false;
    }
    return true;
  }
}
