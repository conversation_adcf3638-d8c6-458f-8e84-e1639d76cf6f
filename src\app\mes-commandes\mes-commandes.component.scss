.orders-container {
    max-width: 1000px;
    margin: auto;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: 80vh;
  }
  
  h1 {
    margin-bottom: 15px;
    font-size: 2rem;
    font-weight: bold;
    color: transparent;
    background: linear-gradient(90deg, #007bff, #4aa5d2);
    -webkit-background-clip: text;
    background-clip: text;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  }
  
  .sections-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
  }
  
  .section-card {
    background: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    width: 100%;
    cursor: pointer;
    transition: transform 0.2s ease-in-out;
  }
  
  .section-card:hover {
    transform: scale(1.05);
  }
  
  table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
  }
  
  th, td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: center;
    color: black;
  }
  
  th {
    background: #007bff;
    color: white;
  }
  
  button {
    padding: 6px 12px;
    margin: 5px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }
  
  .view-btn {
    background-color: #007bff;
    color: white;
  }
  
  .view-btn:hover {
    background-color: #0056b3;
  }
  
  .cancel-btn {
    background-color: #f1f1f1;
    color: #333;
  }
  
  .cancel-btn:hover {
    background-color: #ccc;
  }
  
  .breadcrumb {
    display: flex;
    align-items: center;
    font-size: 16px;
    margin-bottom: 15px;
    color: #007bff;
    text-align: left;
    width: 100%;
  }
  
  .back-link {
    cursor: pointer;
    color: #007bff;
    font-weight: bold;
    text-decoration: none;
    transition: color 0.3s ease;
  }
  
  .back-link:hover {
    color: #0056b3;
    text-decoration: underline;
  }
  .status-pending {
  background-color: #fff3cd; /* jaune clair */
}

.status-confirmed {
  background-color: #d1ecf1; /* bleu clair */
}

.status-preparing {
  background-color: #bee5eb; /* bleu un peu plus foncé */
}

.status-shipped {
  background-color: #c3e6cb; /* vert clair */
}

.status-delivered {
  background-color: #d4edda; /* vert très clair */
}

.status-canceled {
  background-color: #f8d7da; /* rouge clair */
  color: #721c24;
}
