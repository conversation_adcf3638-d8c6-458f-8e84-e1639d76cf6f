import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { UserService } from '../services/user.service';

@Component({
  selector: 'app-user-details',
  templateUrl: './user-details.component.html',
  styleUrls: ['./user-details.component.scss']
})
export class UserDetailsComponent implements OnInit {
  userId!: string;
  selectedUser: any;

  constructor(private route: ActivatedRoute, private userService: UserService) { }

  ngOnInit(): void {
    this.selectedUser = {
      displayName: '',
      email: '',
      phoneNumber: '',
      address: '',
      role: '',
      archived: false
    };
    // Récupérer l'ID de l'utilisateur depuis l'URL
    this.userId = this.route.snapshot.paramMap.get('uid')!;

    // Appeler le service pour récupérer les détails de l'utilisateur
    this.userService.getUserDetails(this.userId).subscribe(
      userDetails => {
        this.selectedUser = userDetails;
        console.log('User details:', this.selectedUser); // Vérification
      },
      error => {
        console.error('Error fetching user details:', error);
      }
    );
  }
  goBack() {
    window.history.back(); // Utilise l'historique du navigateur pour revenir en arrière
  }
  
}
