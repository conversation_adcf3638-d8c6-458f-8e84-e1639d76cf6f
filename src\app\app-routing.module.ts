import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RegisterComponent } from './register/register.component';
import { HomeComponent } from './pages/home/<USER>';
import { PersonalInfoComponent } from './personal-info/personal-info.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { UsersComponent } from './users/users.component';
import { OrdersComponent } from './orders/orders.component';
import { StatisticsComponent } from './statistics/statistics.component';
import { MesCommandesComponent } from './mes-commandes/mes-commandes.component';
import { CommandeDetailComponent } from './commande-detail/commande-detail.component';
import { AProposComponent } from './a-propos/a-propos.component';
import { ProfileComponent } from './profile/profile.component';
import { CommanderComponent } from './commander/commander.component';
import { PaiementComponent } from './paiement/paiement.component';
import { ConfirmationComponent } from './confirmation/confirmation.component';
import { UserDetailsComponent } from './user-details/user-details.component';

import { ProductListComponent } from './product-list/product-list.component';
import { PaymentSuccessComponent } from './payment-success/payment-success.component';
import { ProductDetailListComponent } from './product-detail-list/product-detail-list.component';
import { AuthGuard } from './guards/auth.guard';


const routes: Routes = [
  { path: '', redirectTo: '/home', pathMatch: 'full'},

  { path: 'home', component: HomeComponent,}, // accessible sans connexion

  { path: 'register', component: RegisterComponent }, // accessible sans connexion

  // Routes protégées avec AuthGuard :
  { path: 'personal-info', component: PersonalInfoComponent, canActivate: [AuthGuard] },
  { path: 'dashboard', component: DashboardComponent, canActivate: [AuthGuard] },
  { path: 'users', component: UsersComponent, canActivate: [AuthGuard] },
  { path: 'orders', component: OrdersComponent, canActivate: [AuthGuard] },
  { path: 'statistics', component: StatisticsComponent, canActivate: [AuthGuard] },
  { path: 'mes-commandes', component: MesCommandesComponent, canActivate: [AuthGuard] },
  { path: 'commande-detail/:id', component: CommandeDetailComponent, canActivate: [AuthGuard] },
  { path: 'about-us', component: AProposComponent }, // accessible sans connexion
  { path: 'profile', component: ProfileComponent, canActivate: [AuthGuard] },
  { path: 'commander', component: CommanderComponent, canActivate: [AuthGuard] },
  { path: 'paiement', component: PaiementComponent, canActivate: [AuthGuard] },
  { path: 'confirmation', component: ConfirmationComponent, canActivate: [AuthGuard] },
  { path: 'user-details/:uid', component: UserDetailsComponent, canActivate: [AuthGuard] },
  { path: 'produit/:id', component: ProductDetailListComponent, canActivate: [AuthGuard] },
  { path: 'produits', component: ProductListComponent, canActivate: [AuthGuard] },
  { path: 'payment-success', component: PaymentSuccessComponent, canActivate: [AuthGuard] },
];


@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
