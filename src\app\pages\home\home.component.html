<div class="grid grid-cols-[60%,40%] h-[calc(100vh-4rem)] mt-0">
  <div class="p-5">
    <app-suggested-product></app-suggested-product>
  </div>

  <div class="flex flex-col items-center justify-center gap-4">
    <avatar></avatar>

    <button
      *ngIf="!isListening"
      (click)="toggleSpeech()"
      class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow"
    >
      🎙️ Démarrer le streaming vocala
    </button>

    <div *ngIf="isListening" class="text-center">
      <p class="text-green-700">👂 Écoute en cours... Cliquez pour arrêter :</p>
      <button
        (click)="toggleSpeech()"
        class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded mt-2"
      >
        🛑 Arrêter l'écoute
      </button>
    </div>

    <p class="text-gray-700 font-medium">
      Status: {{ statusMessage }}
    </p>

    

   
  </div>
</div>
