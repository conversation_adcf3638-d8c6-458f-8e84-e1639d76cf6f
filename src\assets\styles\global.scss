.container {
    max-width: 800px;
    margin: auto;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: 80vh;
}

h1 {
    margin-bottom: 15px;
    font-size: 2rem;
    font-weight: bold;
    color: transparent;
    background: linear-gradient(90deg, #007bff, #4aa5d2);
    -webkit-background-clip: text;
    background-clip: text;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

input {
    width: 100%;
    padding: 8px;
    margin-bottom: 15px;
    border: 1px solid #ccc;
    border-radius: 5px;
    color: black;
    background: white;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

th, td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: center;
    color: black;
}

th {
    background: #007bff;
    color: white;
}
