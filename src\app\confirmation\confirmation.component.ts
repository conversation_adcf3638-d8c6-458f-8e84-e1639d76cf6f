// confirmation.component.ts
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ToastNotificationService } from '../services/toast-notification.service'; // Ajustez le chemin selon votre structure

@Component({
  selector: 'app-confirmation',
  templateUrl: './confirmation.component.html',
  styleUrls: ['./confirmation.component.scss']
})
export class ConfirmationComponent implements OnInit {
  
  constructor(
    private router: Router,
    private toastService: ToastNotificationService // <- Injection du service toast
  ) {}

  ngOnInit() {
    this.checkAuthentication();
  }

  private checkAuthentication() {
    const token = localStorage.getItem('token');
    
    if (!token) {
      // Notification d'erreur attirante au lieu d'alert()
      this.toastService.showError(
        'Accès refusé 🔒',
        'Veuillez vous connecter pour accéder à cette page'
      );
      
      // Redirection après un petit délai pour laisser le temps de voir la notification
      setTimeout(() => {
        this.router.navigate(['/login']); // ou vers votre page de connexion
      }, 2000);
      
      return;
    }

    // Si l'utilisateur est connecté, afficher une notification de bienvenue
    this.showWelcomeMessage();
  }

  private showWelcomeMessage() {
    // Notification de succès pour confirmer l'accès à la page
    this.toastService.showSuccess(
      'Commande confirmée ! ✅',
      'Votre commande a été traitée avec succès'
    );
  }

  goHome() {
    // Notification d'information avant la redirection
    this.toastService.showInfo(
      'Redirection en cours... 🏠',
      'Retour vers la page d\'accueil'
    );
    
    // Petit délai pour voir la notification
    setTimeout(() => {
      this.router.navigate(['/home']);
    }, 1000);
  }

  // Méthodes utilitaires pour d'autres actions sur cette page
  downloadReceipt() {
    this.toastService.showSuccess(
      'Téléchargement démarré 📄',
      'Votre reçu est en cours de téléchargement'
    );
    
    // Logique de téléchargement ici
  }

  sendEmailConfirmation() {
    this.toastService.showInfo(
      'Email envoyé 📧',
      'Un email de confirmation a été envoyé à votre adresse'
    );
    
    // Logique d'envoi d'email ici
  }

  shareOrder() {
    this.toastService.showInfo(
      'Lien copié 🔗',
      'Le lien de votre commande a été copié dans le presse-papiers'
    );
    
    // Logique de partage ici
  }

  reportProblem() {
    this.toastService.showWarning(
      'Signalement envoyé ⚠️',
      'Votre signalement a été transmis à notre équipe'
    );
    
    // Logique de signalement ici
  }

  // Exemple d'utilisation avec gestion d'erreur
  async processRefund() {
    try {
      // Simulation d'une requête API
      this.toastService.showInfo(
        'Traitement en cours... ⏳',
        'Votre demande de remboursement est en cours de traitement'
      );

      // Simuler une requête API
      // const result = await this.orderService.processRefund(orderId);
      
      // En cas de succès
      this.toastService.showSuccess(
        'Remboursement initié 💰',
        'Votre remboursement sera traité sous 3-5 jours ouvrables'
      );
      
    } catch (error) {
      // En cas d'erreur
      this.toastService.showError(
        'Erreur de traitement ❌',
        'Impossible de traiter votre demande. Veuillez réessayer.'
      );
    }
  }
}