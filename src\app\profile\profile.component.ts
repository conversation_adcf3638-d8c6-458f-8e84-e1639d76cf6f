import { Component, OnInit } from '@angular/core';
import { UserService } from '../services/user.service';
import { User } from '../model/user.model';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { ToastrService } from 'ngx-toastr';  // Assurez-vous que ToastrService est injecté
import { AuthService } from '../services/auth.service';  // Importez le service d'authentification
import { animate, state, style, transition, trigger } from '@angular/animations';
import { ToastNotificationService } from '../services/toast-notification.service';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],

})
export class ProfileComponent implements OnInit {
  currentUser: User | null = null;
  errorMessage: string = '';
  isEditingCoordinates: boolean = false;
  isEditingPassword: boolean = false;

  // Variables pour l'édition
  editPhoneNumber: string = '';
  editAddress: string = '';
  oldPassword: string = '';
  newPassword: string = '';
  confirmPassword: string = '';
  successMessage: string = ''

  constructor(
    private userService: UserService,
    private afAuth: AngularFireAuth,
    private authService: AuthService,
                    private toastService: ToastNotificationService
      // Injection du service AuthService
  ) {}

  ngOnInit(): void {
    const token = localStorage.getItem('jwt');
    if (!token) {
     this.toastService.showError(
          'Session expirée 🔒',
          'Veuillez vous reconnecter pour accéder à vos commandes'
        );       window.location.href = '/';
      return;
    }
  
    // Attendre que l'utilisateur Firebase soit chargé
    this.afAuth.authState.subscribe(user => {
      if (user) {
        this.loadCurrentUser(); // Charger les détails de l'utilisateur si l'utilisateur Firebase est connecté
      } else {
        this.errorMessage = 'Aucun utilisateur connecté.';
      }
    });
  }
  
  loadCurrentUser(): void {
    this.afAuth.currentUser.then(firebaseUser => {
      if (firebaseUser && firebaseUser.uid) {
        this.userService.getUserDetails(firebaseUser.uid).subscribe(
          (user: User) => {
            this.currentUser = user;
          },
          (error) => {
            console.error('Erreur de récupération du profil:', error);
            this.errorMessage = 'Impossible de charger votre profil.';
          }
        );
      } else {
        this.errorMessage = 'Aucun utilisateur connecté.';
      }
    });
  }

  // Activer l'édition des coordonnées
  enableEditingCoordinates(): void {
    if (this.currentUser?.role !== 'ADMIN') {
      this.isEditingCoordinates = true;
      this.editPhoneNumber = this.currentUser?.phoneNumber || '';
      this.editAddress = this.currentUser?.address || '';
    }
  }

  // Annuler l'édition des coordonnées
  cancelEditingCoordinates(): void {
    this.isEditingCoordinates = false;
  }

  // Sauvegarder les modifications des coordonnées
  saveCoordinates(): void {
    if (this.editPhoneNumber && this.editAddress) {
      const uid = this.currentUser?.uid;  // Récupérer l'UID de l'utilisateur connecté
  
      if (uid) {
        // Appel au service pour enregistrer les nouvelles coordonnées
        this.userService.updateUserCoordinates(uid, this.editPhoneNumber, this.editAddress).subscribe(
          (response) => {
            // Mettre à jour les coordonnées dans le frontend si l'API renvoie un succès
            this.currentUser!.phoneNumber = this.editPhoneNumber;
            this.currentUser!.address = this.editAddress;
            this.isEditingCoordinates = false;
            console.log('Coordonnées mises à jour avec succès!', response);
          },
          (error) => {
            console.error('Erreur lors de la mise à jour des coordonnées:', error);
          }
        );
      }
    }
  }
  
  

  // Activer l'édition du mot de passe
  enableEditingPassword(): void {
    if (this.currentUser?.role !== 'ADMIN') {
      this.isEditingPassword = true;
    }
  }

  // Annuler l'édition du mot de passe
  cancelEditingPassword(): void {
    this.isEditingPassword = false;
  }

  savePassword(): void {
    if (this.newPassword !== this.confirmPassword) {
      this.successMessage = ''; // Clear previous success message
      this.errorMessage = 'Les mots de passe ne correspondent pas';
      return;
    }

    this.afAuth.currentUser.then(user => {
      if (user) {
        user.getIdToken().then(idToken => {
          const uid = user.uid;
          if (this.oldPassword && this.newPassword && uid) {
            this.authService.updatePassword(uid, this.oldPassword, this.newPassword).subscribe({
              next: (response) => {
                this.successMessage = 'Mot de passe mis à jour avec succès';
                this.errorMessage = ''; // Clear previous error message
          if (typeof window !== 'undefined') {
  localStorage.removeItem('jwt');
  localStorage.removeItem('role');
  localStorage.removeItem('userInfo');
  window.location.reload();
}

              },
              error: (error) => {
                this.successMessage = ''; // Clear previous success message
                this.errorMessage = 'Erreur lors de la mise à jour du mot de passe';
              }
            });
          } else {
            this.successMessage = ''; // Clear previous success message
            this.errorMessage = 'Utilisateur ou paramètres manquants';
          }
        });
      }
    });
  }

  
  
  
  
}
