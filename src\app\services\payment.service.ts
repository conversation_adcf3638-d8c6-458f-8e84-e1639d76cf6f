import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class PaymentService {
  private apiUrl = 'http://localhost:8082/api/stripe';

  constructor(private http: HttpClient) {}

createCheckout(amount: number, userId: String): Observable<any> {
  return this.http.post(`${this.apiUrl}/create-checkout-session?amount=${amount}&userId=${userId}`, {});
}


}
