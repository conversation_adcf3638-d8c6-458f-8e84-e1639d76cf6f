<div class="container mx-auto p-6">
  <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">

    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
      <h2 class="text-3xl font-bold mb-2">{{ product?.nom }}</h2>
      <div class="flex items-center space-x-4">
        <span class="bg-white bg-opacity-20 text-gray-900 dark:text-white px-3 py-1 rounded-full text-sm font-medium backdrop-blur-sm shadow-sm">
          <i class="fas fa-tag mr-1"></i>{{ product?.categorie }}
        </span>
        <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm font-medium backdrop-blur-sm"
              [ngClass]="product?.stock > 1 ? 'text-green-600' : 'text-red-600'">
          <i class="fas fa-boxes mr-1"></i>
          {{ product?.stock > 1 ? 'En stock' : 'Hors stock' }}
        </span>
      </div>
    </div>

    <!-- Contenu principal -->
    <div class="grid md:grid-cols-2 gap-8 p-6">

      <!-- Section Images avec Carrousel -->
      <div class="space-y-4">
        
        <!-- Image principale avec carrousel -->
        <div class="relative group">
          <div class="relative overflow-hidden rounded-xl shadow-lg">
            <img [src]="currentImage" 
                 class="w-full h-80 object-cover transition-transform duration-300 hover:scale-105">
            
            <!-- Overlay gradient au hover -->
            <div class="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-0 group-hover:opacity-30 rounded-xl transition-opacity duration-300"></div>
            
            <!-- Boutons navigation -->
            <div *ngIf="product?.imageUrl && product?.imageUrl.length > 1" 
                 class="absolute inset-0 flex items-center justify-between p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <button (click)="previousImage()" 
                      class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white p-3 rounded-full transition-all duration-200 shadow-lg">
                <i class="fas fa-chevron-left text-lg"></i>
              </button>
              <button (click)="nextImage()" 
                      class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white p-3 rounded-full transition-all duration-200 shadow-lg">
                <i class="fas fa-chevron-right text-lg"></i>
              </button>
            </div>
            
            <!-- Indicateurs de pagination -->
            <div *ngIf="product?.imageUrl && product?.imageUrl.length > 1" 
                 class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
              <button *ngFor="let imageUrl of product?.imageUrl; let i = index"
                      (click)="setCurrentImage(i)"
                      class="w-3 h-3 rounded-full transition-all duration-200 border-2 border-white"
                      [ngClass]="currentImageIndex === i ? 'bg-white' : 'bg-transparent hover:bg-white hover:bg-opacity-50'">
              </button>
            </div>

            <!-- Badge nombre d'images -->
            <div *ngIf="product?.imageUrl && product?.imageUrl.length > 1" 
                 class="absolute top-4 left-4 bg-black bg-opacity-60 text-white px-3 py-1 rounded-full text-sm font-medium">
              <i class="fas fa-images mr-1"></i>
              {{ product.imageUrl.length }}
            </div>
          </div>
        </div>
        
        <!-- Miniatures des images -->
        <div *ngIf="product?.imageUrl && product?.imageUrl.length > 1" 
             class="space-y-2">
          <h4 class="text-sm font-medium text-gray-700 flex items-center">
            <i class="fas fa-th mr-2"></i>
            Voir toutes les images
          </h4>
          <div class="flex space-x-3 overflow-x-auto pb-2 scrollbar-hide">
            <button *ngFor="let imageUrl of product?.imageUrl; let i = index"
                    (click)="setCurrentImage(i)"
                    class="flex-shrink-0 relative overflow-hidden rounded-lg border-2 transition-all duration-200 hover:scale-105"
                    [ngClass]="currentImageIndex === i ? 'border-blue-500 shadow-lg ring-2 ring-blue-200' : 'border-gray-200 hover:border-gray-300'">
              <img [src]="imageUrl" 
                   [alt]="product.nom + ' - Vue ' + (i + 1)"
                   class="w-20 h-20 object-cover">
              <!-- Overlay pour l'image active -->
              <div *ngIf="currentImageIndex === i" 
                   class="absolute inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center">
                <i class="fas fa-check text-blue-600 text-lg"></i>
              </div>
              <!-- Numéro de l'image -->
              <div class="absolute bottom-1 right-1 bg-black bg-opacity-60 text-white text-xs px-1 rounded">
                {{ i + 1 }}
              </div>
            </button>
          </div>
          
          <!-- Navigation par flèches pour les miniatures -->
          <div class="flex justify-between items-center text-sm text-gray-500">
            <button (click)="scrollThumbnails(-1)" 
                    class="flex items-center hover:text-gray-700 transition-colors">
              <i class="fas fa-chevron-left mr-1"></i>
              Précédent
            </button>
            <span class="font-medium">
              {{ currentImageIndex + 1 }} / {{ product.imageUrl.length }}
            </span>
            <button (click)="scrollThumbnails(1)" 
                    class="flex items-center hover:text-gray-700 transition-colors">
              Suivant
              <i class="fas fa-chevron-right ml-1"></i>
            </button>
          </div>
        </div>

        <!-- Vue en plein écran (optionnel) -->
        <button *ngIf="product?.imageUrl && product?.imageUrl.length > 0"
                (click)="openFullscreen()"
                class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-lg transition-colors duration-200 text-sm font-medium">
          <i class="fas fa-expand mr-2"></i>
          Voir en plein écran
        </button>
        
        <!-- Prix avec design attractif -->
        <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border border-green-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 mb-1">Prix</p>
              <!-- Prix promotion si disponible -->
              <div *ngIf="product?.prixPromotion && product?.prixPromotion < product?.prix" class="space-y-1">
                <p class="text-3xl font-bold text-green-600">{{ product.prixPromotion | currency:'EUR':'symbol':'1.2-2':'fr-FR' }}</p>
                <p class="text-lg line-through text-gray-500">{{ product.prix | currency:'EUR':'symbol':'1.2-2':'fr-FR' }}</p>
                <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                  PROMO -{{ getDiscountPercentage() }}%
                </span>
              </div>
              <!-- Prix normal -->
              <p *ngIf="!(product?.prixPromotion && product?.prixPromotion < product?.prix)" 
                 class="text-3xl font-bold text-green-600">{{ product?.prix | currency:'EUR':'symbol':'1.2-2':'fr-FR' }}</p>
            </div>
            <div class="bg-green-500 text-white p-4 rounded-full">
              <i class="fas fa-euro-sign text-xl"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- Détails -->
      <div class="space-y-6">

        <!-- Caractéristiques -->
        <div class="bg-purple-50 p-4 rounded-xl border border-purple-200">
          <h3 class="flex items-center text-lg font-semibold text-purple-800 mb-2">
            <i class="fas fa-list-ul mr-3 text-purple-600"></i>
            Caractéristiques
          </h3>
          <p class="text-purple-700">{{ product?.caracteristiques }}</p>
         </div>

        <!-- Autres infos (couleur, style, matière...) -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">

          <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h4 class="flex items-center font-semibold text-gray-800 mb-2">
              <i class="fas fa-palette mr-2 text-pink-500"></i>
              Couleurs
            </h4>
            <div class="flex flex-wrap gap-1">
              <span *ngFor="let couleur of product?.couleur"
                    class="bg-pink-100 text-pink-800 px-2 py-1 rounded-full text-xs font-medium">
                {{ couleur }}
              </span>
            </div>
          </div>

          <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h4 class="flex items-center font-semibold text-gray-800 mb-2">
              <i class="fas fa-gem mr-2 text-orange-500"></i>
              Matière
            </h4>
            <p class="text-gray-700 text-sm">{{ product?.matiere }}</p>
          </div>

          <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h4 class="flex items-center font-semibold text-gray-800 mb-2">
              <i class="fas fa-calendar-alt mr-2 text-green-500"></i>
              Occasions
            </h4>
            <div class="flex flex-wrap gap-1">
              <span *ngFor="let occasion of product?.occasion"
                    class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                {{ occasion }}
              </span>
            </div>
          </div>

          <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h4 class="flex items-center font-semibold text-gray-800 mb-2">
              <i class="fas fa-tshirt mr-2 text-purple-500"></i>
              Styles
            </h4>
            <div class="flex flex-wrap gap-1">
              <span *ngFor="let style of product?.style"
                    class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium">
                {{ style }}
              </span>
            </div>
          </div>

          <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h4 class="flex items-center font-semibold text-gray-800 mb-2">
              <i class="fas fa-ruler mr-2 text-blue-500"></i>
              Tailles
            </h4>
            <div class="flex flex-wrap gap-1">
              <span *ngFor="let taille of product?.taille"
                    class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                {{ taille }}
              </span>
            </div>
          </div>

          <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h4 class="flex items-center font-semibold text-gray-800 mb-2">
              <i class="fas fa-sun mr-2 text-yellow-500"></i>
              Saison
            </h4>
            <p class="text-gray-700 text-sm">{{ product?.saison }}</p>
          </div>

        </div>
<div *ngIf="product?.avisCommentaires?.length > 0">
  <h4 class="flex items-center font-bold text-black mb-3">
    <i class="fas fa-bolt mr-2 text-black"></i>
    Avis
  </h4>

  <div *ngFor="let avisStr of product.avisCommentaires" class="bg-blue-50 p-4 rounded-xl border border-blue-200 mt-4">
    <ng-container *ngIf="parseAvis(avisStr) as avisObj">
      <p><strong class="font-bold text-black">Commentaire :</strong> <span class="text-black font-normal">{{ avisObj.commentaire }}</span></p>
      <p><strong class="font-bold text-black">Note :</strong> <span class="text-black font-normal">{{ avisObj.avis }} / 5</span></p>
      <p><strong class="font-bold text-black">Nom :</strong> <span class="text-black font-normal">{{ avisObj.nom }}</span></p>
    </ng-container>
  </div>
</div>








        <!-- Boutons -->
        <div class="flex gap-3 pt-4">
          <button (click)="addToCart(product)"
                  class="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
            <i class="fas fa-shopping-cart mr-2"></i>
            Ajouter au panier
          </button>
          <button (click)="addToFavorites(product)"
                  class="bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-6 rounded-xl transition-colors duration-200">
            <i class="fas fa-heart mr-2"></i>
            Favoris
          </button>
        </div>
      </div>
    </div>
  </div>
</div>