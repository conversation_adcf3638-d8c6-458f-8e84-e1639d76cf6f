.orders-container {
    max-width: 1000px;
    margin: auto;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: 80vh;
  }
  
  h1 {
    margin-bottom: 15px;
    font-size: 2rem;
    font-weight: bold;
    color: transparent;
    background: linear-gradient(90deg, #007bff, #4aa5d2);
    -webkit-background-clip: text;
    background-clip: text;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  }
  
  input {
    width: 100%;
    padding: 8px;
    margin-bottom: 15px;
    border: 1px solid #ccc;
    border-radius: 5px;
    color: black;
    background: white;
  }
  
  .sections-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
  }
  
  .section-card {
    background: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    width: 100%;
    cursor: pointer;
    transition: transform 0.2s ease-in-out;
  }
  
  .section-card:hover {
    transform: scale(1.05);
  }
  
  table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
  }
  
  th, td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: center;
    color: black;
  }
  
  th {
    background: #007bff;
    color: white;
  }
  
  .actions {
    margin-top: 10px;
  }
  
  button {
    padding: 6px 12px;
    margin: 5px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }
  
  .view-btn {
    background-color: #007bff;
    color: white;
  }
  
  .view-btn:hover {
    background-color: #0056b3;
  }
  
  .archive-btn {
    background-color: #f1f1f1;
    color: #333;
  }
  
  .archive-btn:hover {
    background-color: #ccc;
  }
  
  .breadcrumb {
    display: flex;
    align-items: center;
    font-size: 16px;
    margin-bottom: 15px;
    color: #007bff;
    text-align: left;
    width: 100%;
  }
  
  .back-link {
    cursor: pointer;
    color: #007bff;
    font-weight: bold;
    text-decoration: none;
    transition: color 0.3s ease;
  }
  
  .back-link:hover {
    color: #0056b3;
    text-decoration: underline;
  }
  .status-pending {
  background-color: #fff3cd; /* jaune clair */
}

.status-confirmed {
  background-color: #d1ecf1; /* bleu clair */
}

.status-preparing {
  background-color: #bee5eb; /* bleu un peu plus foncé */
}

.status-shipped {
  background-color: #c3e6cb; /* vert clair */
}

.status-delivered {
  background-color: #d4edda; /* vert très clair */
}

.status-canceled {
  background-color: #f8d7da; /* rouge clair */
  color: #721c24;
}
/* orders.component.css */

.orders-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.breadcrumb {
  margin-bottom: 20px;
  font-size: 14px;
  color: #666;
}

.back-link {
  color: #007bff;
  cursor: pointer;
  text-decoration: underline;
}

.back-link:hover {
  color: #0056b3;
}

.sections-container {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.status-section {
  width: 100%;
}

.section-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.status-title {
  padding: 15px 20px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  border-bottom: 2px solid #eee;
}

/* Classes de statut pour les titres */
.status-title.status-pending {
  background-color: #fff3cd;
  color: #856404;
  border-bottom-color: #ffeaa7;
}

.status-title.status-confirmed {
  background-color: #d4edda;
  color: #155724;
  border-bottom-color: #74b9ff;
}

.status-title.status-preparing {
  background-color: #cce5ff;
  color: #004085;
  border-bottom-color: #0984e3;
}

.status-title.status-shipped {
  background-color: #e7f3ff;
  color: #0c5460;
  border-bottom-color: #00b894;
}

.status-title.status-delivered {
  background-color: #d1f2eb;
  color: #0c5460;
  border-bottom-color: #00b894;
}

.status-title.status-canceled {
  background-color: #f8d7da;
  color: #721c24;
  border-bottom-color: #e17055;
}

.status-title.status-archived {
  background-color: #f8f9fa;
  color: #495057;
  border-bottom-color: #6c757d;
}

table {
  width: 100%;
  border-collapse: collapse;
}

thead th {
  background-color: #f8f9fa;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  border-bottom: 1px solid #dee2e6;
  color: #000000;
}

tbody td {
  padding: 12px;
  border-bottom: 1px solid #f1f3f4;
}

/* Classes de statut pour les lignes */
tbody tr.status-pending {
  background-color: rgba(255, 243, 205, 0.3);
}

tbody tr.status-confirmed {
  background-color: rgba(212, 237, 218, 0.3);
}

tbody tr.status-preparing {
  background-color: rgba(204, 229, 255, 0.3);
}

tbody tr.status-shipped {
  background-color: rgba(231, 243, 255, 0.3);
}

tbody tr.status-delivered {
  background-color: rgba(209, 242, 235, 0.3);
}

tbody tr.status-canceled {
  background-color: rgba(248, 215, 218, 0.3);
}

tbody tr.status-archived {
  background-color: rgba(248, 249, 250, 0.3);
}

.view-btn, .archive-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 5px;
  font-size: 12px;
}

.view-btn {
  background-color: #007bff;
  color: white;
}

.view-btn:hover {
  background-color: #0056b3;
}

.archive-btn {
  background-color: #6c757d;
  color: white;
}

.archive-btn:hover {
  background-color: #545b62;
}

select {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

input[type="text"] {
  width: 300px;
  padding: 10px;
  margin-bottom: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}