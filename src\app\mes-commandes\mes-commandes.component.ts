import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { OrderService, OrderStatus } from '../services/order.service';
import { Order } from '../model/order.model';
import { ToastNotificationService } from '../services/toast-notification.service';

@Component({
  selector: 'app-mes-commandes',
  templateUrl: './mes-commandes.component.html',
  styleUrls: ['./mes-commandes.component.scss']
})
export class MesCommandesComponent implements OnInit {
  selectedOrder: any = null;
  orders: Order[] = [];
  loading = false; // Ajout d'un état de chargement
OrderStatus = OrderStatus;
  constructor(
    private router: Router,
    private afAuth: AngularFireAuth,
    private orderService: OrderService,
    private toastService: ToastNotificationService
  ) {}

  ngOnInit(): void {
    const token = localStorage.getItem('jwt');
    const role = localStorage.getItem('role');

    if (!token || (role?.toUpperCase() !== 'USER' && role?.toUpperCase() !== 'ADMIN')) {
      this.toastService.showError(
        'Accès refusé 🔒',
        'Seuls les utilisateurs connectés peuvent voir leurs commandes'
      );
      this.router.navigate(['/']);
      return;
    }

    this.afAuth.authState.subscribe(user => {
      if (!user) {
        this.toastService.showError(
          'Session expirée 🔒',
          'Veuillez vous reconnecter pour accéder à vos commandes'
        );
        this.router.navigate(['/']);
      } else {
        // User is logged in, fetch orders
        const userId = user.uid;
        this.loadOrders(userId);
      }
    });
  }

  loadOrders(userId: string): void {
    this.loading = true;
    
    // Notification de chargement
 
    this.orderService.getOrdersByUser(userId).subscribe({
      next: (orders) => {
        this.orders = orders;
        this.loading = false;
        
        // Notification de succès
        if (orders.length > 0) {
         
        } else {
        
        }
      },
      error: (err) => {
        console.error('Erreur lors du chargement des commandes', err);
        this.loading = false;
        
        // ❌ Remplacement de alert() par toast
       
      }
    });
  }

  viewOrder(order: Order) {
    // Notification d'information
  
    
    setTimeout(() => {
      this.router.navigate(['/commande-detail', order.id]);
    }, 800);
  }

  goToAccueil() {
   
    
    setTimeout(() => {
      this.router.navigate(['/home']);
    }, 800);
  }
cancelOrder(orderId: string) {
  const order = this.orders.find(o => o.id === orderId);

  if (!order) {
    console.error('Commande introuvable');
    return;
  }

  // ✅ Vérifier le statut avant d’annuler
  if (order.status !== OrderStatus.EN_ATTENTE) {
    this.toastService.showError(
      'Annulation refusée ❌',
      'Vous ne pouvez annuler que les commandes en attente.'
    );
    return;
  }

  // ✅ Si en attente, procéder à l’annulation
  this.orderService.updateOrderStatus(orderId, OrderStatus.ANNULEE).subscribe({
    next: (updatedOrder: Order) => {
      const index = this.orders.findIndex(o => o.id === orderId);
      if (index !== -1) {
        this.orders[index].status = OrderStatus.ANNULEE;
      }
      this.toastService.showSuccess('Commande annulée ✅', 'Votre commande a été annulée avec succès');
    },
    error: (err: any) => {
      console.error('Erreur lors de l\'annulation de la commande:', err);
      this.toastService.showError('Erreur d\'annulation ❌', 'Impossible d\'annuler la commande. Veuillez réessayer.');
    }
  });
}


  // Méthode pour consulter une commande (si vous la réactivez)
  consultOrder(orderId: string) {
  

    this.orderService.getOrderById(orderId).subscribe({
      next: (order) => {
        this.selectedOrder = order;
        console.log('Order details:', order);
        
    
      },
      error: (error) => {
        console.error('Failed to fetch order details', error);
        
        this.toastService.showError(
          'Erreur de chargement ❌',
          'Impossible de récupérer les détails de la commande'
        );
      }
    });
  }

  closeOrderDetails() {
    this.selectedOrder = null;
    

  }

  // Méthodes utilitaires supplémentaires
  retryLoadOrders() {
    this.afAuth.currentUser.then(user => {
      if (user?.uid) {
        this.loadOrders(user.uid);
      }
    });
  }

  refreshOrders() {
    this.toastService.showInfo(
      'Actualisation... 🔄',
      'Mise à jour de vos commandes'
    );
    
    this.retryLoadOrders();
  }

  exportOrders() {
    this.toastService.showSuccess(
      'Export démarré 📊',
      'Vos commandes sont en cours d\'exportation'
    );
    
    // Logique d'export ici
  }

  contactSupport() {
    this.toastService.showInfo(
      'Support contacté 💬',
      'Notre équipe vous répondra dans les plus brefs délais'
    );
    
    // Logique de contact support ici
  }

  // Méthode pour gérer les erreurs réseau de façon générale
  handleNetworkError(error: any, action: string) {
    if (error.status === 0) {
      this.toastService.showError(
        'Connexion impossible 🌐',
        'Vérifiez votre connexion internet'
      );
    } else if (error.status >= 500) {
      this.toastService.showError(
        'Erreur serveur 🔧',
        'Nos serveurs rencontrent des difficultés. Réessayez plus tard.'
      );
    } else if (error.status === 401) {
      this.toastService.showWarning(
        'Session expirée 🔐',
        'Veuillez vous reconnecter'
      );
      this.router.navigate(['/login']);
    } else {
      this.toastService.showError(
        `Erreur ${action} ❌`,
        'Une erreur inattendue s\'est produite'
      );
    }
  }
  getStatusClass(status: string): string {
  switch (status) {
    case 'EN_ATTENTE': return 'status-pending';
    case 'CONFIRMÉE': return 'status-confirmed';
    case 'EN_PRÉPARATION': return 'status-preparing';
    case 'EXPÉDIÉE': return 'status-shipped';
    case 'LIVRÉE': return 'status-delivered';
    case 'ANNULÉE': return 'status-canceled';
    default: return '';
  }
}

}