import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class SpeechWebsocketService {
  private ws: WebSocket | null = null;
  private readonly WS_URL = 'ws://localhost:8082/ws/speech'; // adapte l'URL à ton port

  connect(onMessage: (response: string) => void): void {
    this.ws = new WebSocket(this.WS_URL);

    this.ws.binaryType = 'arraybuffer';

    this.ws.onopen = () => {
      console.log('🎤 WebSocket connecté à SpeechService');
    };

    this.ws.onmessage = (event) => {
      const message = event.data;
      console.log('📩 Message reçu : ', message);
      onMessage(message); // traitement côté composant
    };

    this.ws.onerror = (error) => {
      console.error('❌ WebSocket erreur : ', error);
    };

    this.ws.onclose = () => {
      console.warn('🔌 WebSocket fermé');
    };
  }

  sendAudioChunk(audioChunk: ArrayBuffer): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(audioChunk);
    } else {
      console.warn('⚠️ WebSocket non prêt, chunk ignoré');
    }
  }

  close(): void {
    this.ws?.close();
  }
}
