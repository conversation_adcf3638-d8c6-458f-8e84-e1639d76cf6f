import { LOCALE_ID, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { ProductListComponent } from './product-list/product-list.component';
import { HomeComponent } from './pages/home/<USER>';
import { NavbarComponent } from './navbar/navbar.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RegisterComponent } from './register/register.component';
import { PersonalInfoComponent } from './personal-info/personal-info.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { UsersComponent } from './users/users.component';
import { OrdersComponent } from './orders/orders.component';
import { StatisticsComponent } from './statistics/statistics.component';
import { SidebarComponent } from './sidebar/sidebar.component';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faList, faUsers, faChartBar, faInfoCircle, faUser, faSignOutAlt } from '@fortawesome/free-solid-svg-icons';
import { library } from '@fortawesome/fontawesome-svg-core';
import { MesCommandesComponent } from './mes-commandes/mes-commandes.component';
import { CommandeDetailComponent } from './commande-detail/commande-detail.component';
import { AProposComponent } from './a-propos/a-propos.component';
import { ProfileComponent } from './profile/profile.component';
import { CommanderComponent } from './commander/commander.component';
import { PaiementComponent } from './paiement/paiement.component';
import { ConfirmationComponent } from './confirmation/confirmation.component';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { AngularFireModule } from '@angular/fire/compat';
import { AngularFireAuthModule } from '@angular/fire/compat/auth';
import { environment } from '../environments/environment';
import { initializeApp } from '@angular/fire/app';
import { ToastrModule } from 'ngx-toastr';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { JWT_OPTIONS, JwtHelperService } from '@auth0/angular-jwt';
import { AuthInterceptor } from './interceptors/auth.interceptor';
import { UserDetailsComponent } from './user-details/user-details.component';
import { AvatarComponent } from './avatar/avatar.component';

import { SuggestedProductComponent } from './components/suggested-product/suggested-product.component';
import { PaymentSuccessComponent } from './payment-success/payment-success.component';
import { ProductDetailComponent } from './product-detail/product-detail.component';
import { registerLocaleData } from '@angular/common';
import localeFr from '@angular/common/locales/fr';
import { ProductDetailListComponent } from './product-detail-list/product-detail-list.component';
import { ToastNotificationComponent } from './toast-notification/toast-notification.component';

registerLocaleData(localeFr);


@NgModule({
  declarations: [
    AppComponent,
    AvatarComponent,
    ProductListComponent,
    HomeComponent,
    NavbarComponent,
    RegisterComponent,
    PersonalInfoComponent,
    DashboardComponent,
    UsersComponent,
    OrdersComponent,
    StatisticsComponent,
    SidebarComponent,
    MesCommandesComponent,
    CommandeDetailComponent,
    AProposComponent,
    ProfileComponent,
    CommanderComponent,
    PaiementComponent,
    ConfirmationComponent,
    UserDetailsComponent,

    SuggestedProductComponent,
    PaymentSuccessComponent,
    ProductDetailComponent,
    ProductDetailListComponent,
    ToastNotificationComponent,

  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    ReactiveFormsModule,
    FormsModule,
    FontAwesomeModule,
    HttpClientModule,
    AngularFireModule.initializeApp(environment.firebase),  // Firebase initialisation
    AngularFireAuthModule, // Authentification Firebase

    BrowserAnimationsModule,
ToastrModule.forRoot({
  positionClass: 'toast-center-center',
  timeOut: 4000,
  easeTime: 500,
  progressBar: true,
  closeButton: true,
  enableHtml: true,
  preventDuplicates: true,
  tapToDismiss: true,
  extendedTimeOut: 1000,
  progressAnimation: 'increasing'
})

   
  ],
providers: [
  { provide: JWT_OPTIONS, useValue: JWT_OPTIONS },
  { provide: LOCALE_ID, useValue: 'fr-FR' },
  JwtHelperService,
  {
    provide: HTTP_INTERCEPTORS,
    useClass: AuthInterceptor,
    multi: true
  }
],
  bootstrap: [AppComponent]
})
export class AppModule {
  constructor() {
    // Ajout des icônes à la bibliothèque FontAwesome
    library.add(faList, faUsers, faChartBar, faInfoCircle, faUser, faSignOutAlt);
    
  }
}
