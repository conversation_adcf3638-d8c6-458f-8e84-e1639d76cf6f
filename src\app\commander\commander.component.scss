// Styles existants de votre composant
.commande-card {
  background: #fff;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 450px;
  margin: 2rem auto;
  text-align: center;
  transition: filter 0.3s ease;
}

h2 {
  margin-bottom: 15px;
  font-size: 2rem;
  font-weight: bold;
  color: transparent;
  background: linear-gradient(90deg, #007bff, #4aa5d2);
  -webkit-background-clip: text;
  background-clip: text;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.address-section {
  margin: 2rem 0;
  
  h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 1.5rem;
    font-weight: 600;
  }
}

.address-option {
  margin-bottom: 1.5rem;
  text-align: left;
}

.adresse-existante {
  font-size: 1rem;
  background: #f9f9f9;
  padding: 0.8rem;
  border-radius: 8px;
  border: 1px solid #ddd;
  color: #555;
  margin-bottom: 0.8rem;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem 0;
}

input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

label {
  font-size: 1rem;
  color: #444;
  cursor: pointer;
}

.new-address-input {
  margin-top: 1rem;
}

.input-field {
  width: 100%;
  padding: 0.8rem;
  margin-top: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 1rem;
  color: black;
  box-sizing: border-box;
}

.btn-commander {
  background: #007bff;
  color: white;
  padding: 0.8rem;
  width: 100%;
  border: none;
  border-radius: 8px;
  font-size: 1.2rem;
  cursor: pointer;
  transition: background 0.3s ease;
  margin-top: 1rem;
}

.btn-commander:hover {
  background: #0056b3;
}

// Styles pour les alertes attirantes
.alert-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.custom-alert {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  min-width: 400px;
  max-width: 500px;
  margin: 20px;
  animation: slideUp 0.3s ease-out;
  overflow: hidden;
  border-left: 6px solid;

  &.alert-warning {
    border-left-color: #ff9800;
    .alert-header {
      background: linear-gradient(135deg, #ff9800, #ffb74d);
    }
    .btn-primary {
      background: linear-gradient(135deg, #ff9800, #ffb74d);
    }
  }

  &.alert-error {
    border-left-color: #f44336;
    .alert-header {
      background: linear-gradient(135deg, #f44336, #ef5350);
    }
    .btn-primary {
      background: linear-gradient(135deg, #f44336, #ef5350);
    }
  }

  &.alert-success {
    border-left-color: #4caf50;
    .alert-header {
      background: linear-gradient(135deg, #4caf50, #66bb6a);
    }
    .btn-primary {
      background: linear-gradient(135deg, #4caf50, #66bb6a);
    }
  }

  &.alert-info {
    border-left-color: #2196f3;
    .alert-header {
      background: linear-gradient(135deg, #2196f3, #42a5f5);
    }
    .btn-primary {
      background: linear-gradient(135deg, #2196f3, #42a5f5);
    }
  }
}

.alert-header {
  padding: 20px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  .alert-title {
    display: flex;
    align-items: center;
    gap: 12px;

    .alert-icon {
      font-size: 28px;
    }

    h3 {
      margin: 0;
      font-size: 20px;
      font-weight: 700;
      color: white;
    }
  }

  .close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }
}

.alert-content {
  padding: 24px;
  
  p {
    margin: 0;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    text-align: center;
  }
}

.alert-actions {
  padding: 0 24px 24px;
  display: flex;
  justify-content: center;

  .btn-primary {
    border: none;
    color: white;
    padding: 12px 32px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

// Effet de flou sur le contenu en arrière-plan
.blur-background {
  filter: blur(3px);
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// Style responsive
@media (max-width: 768px) {
  .commande-card {
    margin: 1rem;
    padding: 1.5rem;
    max-width: none;
  }

  .custom-alert {
    min-width: auto;
    max-width: 90%;
    margin: 10px;
  }

  .alert-content p {
    font-size: 14px;
  }

  .btn-primary {
    padding: 10px 24px !important;
    font-size: 14px !important;
  }

  .alert-title h3 {
    font-size: 18px !important;
  }
}