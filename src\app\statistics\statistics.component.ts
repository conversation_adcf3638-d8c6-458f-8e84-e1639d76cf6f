import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service'; // Assurez-vous que vous avez un service pour gérer l'authentification.

@Component({
  selector: 'app-statistics',
  templateUrl: './statistics.component.html',
  styleUrls: ['./statistics.component.scss']
})
export class StatisticsComponent implements OnInit {
  totalUsers: number = 125;
  totalOrders: number = 450;
  totalRevenue: number = 12000; // Exemple en euros

  constructor(private router: Router, private authService: AuthService) { }

  ngOnInit(): void {
    // Vérifier si l'utilisateur est connecté et s'il est administrateur
    const user = this.authService.getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      // Rediriger l'utilisateur si ce n'est pas un administrateur
      this.router.navigate(['/home']); // ou une autre page de votre choix
    }
  }

  goToDashboard() {
    this.router.navigate(['/dashboard']);
  }
}
