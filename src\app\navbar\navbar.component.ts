import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { HttpErrorResponse } from '@angular/common/http';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { CartItem } from '../model/cart-item.model';
import { CartService } from '../services/cart.service';
import { Product } from '../model/produit.model';
import { FavoriteItem } from '../model/favorite-item.model';
import { FavoriteService } from '../services/favorite.service';
import { AuthModalService } from '../model/auth-modal.service';
import { Subscription } from 'rxjs';
import { ToastNotificationService } from '../services/toast-notification.service';

@Component({
  selector: 'navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss']
})
export class NavbarComponent implements OnInit {

  cartItems: CartItem[] = [];
  isModalOpen = false;
  isLoggedIn = false;
  isCartOpen = false;
  loginForm: FormGroup;
  email: string = '';
  password: string = '';
  userDisplayName: string = '';
  isMenuOpen = false;
  role: string = '';
  isFavoritesOpen = false;
  favoriteItems: any[] = [];
  produits: Product[] = [];
  amount: number = 0;
private subscription!: Subscription;
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private authService: AuthService,
    private afAuth: AngularFireAuth,
    private cartService: CartService,
    private favoriteService: FavoriteService,
    private cdr: ChangeDetectorRef,
    private authModalService: AuthModalService,
        private toastService: ToastNotificationService

  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', Validators.required]
    });

    this.afAuth.authState.subscribe(user => {
      if (this.isLocalStorageAvailable()) {
        const jwt = localStorage.getItem('jwt');
        const role = localStorage.getItem('role');

        if (user && jwt) {
          this.isLoggedIn = true;
          this.userDisplayName = user.displayName || user.email || 'Utilisateur';
          this.role = role || 'user';
        } else {
          this.isLoggedIn = false;
          this.userDisplayName = '';
          this.role = '';
        }
      } else {
        this.isLoggedIn = false;
        this.userDisplayName = '';
        this.role = '';
      }
    });
  }

  ngOnInit() {
      this.subscription = this.authModalService.openModal$.subscribe(() => {
      this.isModalOpen = true;
    })
    this.cartService.getCart().subscribe(items => {
      this.cartItems = items;
      this.amount = this.cartService.getTotalAmount();
      this.cdr.detectChanges();
    });
    this.favoriteService.getFavorites().subscribe(items => {
      this.favoriteItems = items;
      this.cdr.detectChanges();
    });
  }
    ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  private isLocalStorageAvailable(): boolean {
    try {
      return typeof localStorage !== 'undefined' && localStorage !== null;
    } catch {
      return false;
    }
  }

  toggleCart() {
    this.isCartOpen = !this.isCartOpen;
  }

  updateQuantity(item: CartItem): void {
    if (item.quantity < 1) item.quantity = 1;
    this.cartService.updateCart(); // Émet le changement, ne réajoute pas
  }

  removeItem(item: CartItem): void {
    this.cartService.removeItem(item.productId);
  }

  goToCommande() {
    if (this.isLocalStorageAvailable()) {
      const role = localStorage.getItem('role');
      if (this.isLoggedIn && (role === 'USER' || role === 'ADMIN')) {
        this.isCartOpen = false;
        this.router.navigate(['/commander']);
      } else {
        this.isCartOpen = false;
        this.openModal();
      }
    } else {
      this.isCartOpen = false;
      this.openModal();
    }
  }

  openModal() {
    this.isModalOpen = true;
  }

  closeModal() {
    this.isModalOpen = false;
    this.loginForm.reset();
  }

  async login() {
    await this.afAuth.signOut();
    this.authService.loginWithEmail(this.email, this.password).subscribe(
      async (response) => {
        if (response?.archived) {
             this.toastService.showError(
          '❌ Votre compte est',
          ' archivé. Connexion refusée'
        );
          return;
        }

        if (response?.jwt && response?.firebaseToken) {
          if (this.isLocalStorageAvailable()) {
            localStorage.setItem('jwt', response.jwt);
          }
          try {
            const userCredential = await this.afAuth.signInWithCustomToken(response.firebaseToken);
            const user = userCredential.user;
            if (user) {
              this.isLoggedIn = true;
              this.userDisplayName = user.displayName || user.email || 'Utilisateur';
              this.role = response.role || 'user';
              if (this.isLocalStorageAvailable()) {
                localStorage.setItem('role', this.role);
              }

              this.closeModal();
              window.location.reload();
            }
          } catch (firebaseError) {
            console.error('Erreur de connexion Firebase :', firebaseError);
                   this.toastService.showError(
          '❌ Erreur de connexion',
          ' Veuillez réessayer.'
        );
          }
        } else {
             this.toastService.showError(
          '❌ Erreur de connexion',
          ' Veuillez réessayer.'
        );        }
      },
      (error: HttpErrorResponse) => {
        if (error.status === 403) {
     this.toastService.showError(
          '❌ Votre compte est',
          ' archivé. Connexion refusée'
        );        } else if (error.status === 404) {
               this.toastService.showError(
          '🚫Utilisateur',
          ' introuvable.'
        );
        } else {
this.toastService.showError(
          '❌ virefier votre email',
          ' ou mot de passe'
        );         }
      }
    );
  }

  logout() {
    if (this.isLocalStorageAvailable()) {
      localStorage.removeItem('jwt');
      localStorage.removeItem('role');
      localStorage.removeItem('userInfo');
    }
    this.isLoggedIn = false;
    this.userDisplayName = '';
    this.isMenuOpen = false;
    this.toastService.showSuccess(
          'Déconnexion ',
          'réussie ! 👋'
        );
    this.afAuth.signOut();
      
      
    window.location.href = '/home';
  }

  toggleMenu() {
    this.isMenuOpen = !this.isMenuOpen;
  }

  goToProfile() {
    this.isMenuOpen = false;
    this.router.navigate(['/profile']);
  }

  goToOrders() {
    this.isMenuOpen = false;
    this.router.navigate(['/mes-commandes']);
  }

  closeMenu() {
    this.isMenuOpen = false;
  }

  isAdmin(): boolean {
    return this.role === 'ADMIN';
  }

  toggleFavorites() {
    this.isFavoritesOpen = !this.isFavoritesOpen;
  }

  addToCart(item: any) {
  if (!item.productId || !item.images || !item.nom || !item.prix) {
    console.error('Produit incomplet, impossible d\'ajouter au panier :', item);
    return;
  }

  const product = {
    productId: item.productId,
    nom: item.nom,
    prix: item.prix,
    quantity: 1,
    images: item.images
  };

  this.cartService.addToCart(product);
this.toastService.showSuccess(
        'Ajouté au panier ! 🛒',
        `${product.nom} a été ajouté à votre panier avec succès`
      );
  console.log('Ajouté au panier :', product);
}


  removeFromFavorites(item: FavoriteItem): void {
    this.favoriteService.removeFromFavorites(item.productId);
      this.toastService.showSuccess(
          'Produit ',
          'a été retiré des favoriss✅'
        );
      
  }

}
