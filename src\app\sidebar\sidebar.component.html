<div class="sidebar" [ngClass]="{ 'collapsed': isCollapsed }">
  <!-- Logo et bouton de bascule -->
  <div class="sidebar-header">
    <div class="logo">
      <a routerLink="/home">
        <img src="assets/heracles-removebg-preview.png" alt="Logo" class="logo-img">
      </a>
    </div>
    <button class="toggle-btn" (click)="toggleSidebar()">
      <fa-icon [icon]="faBars"></fa-icon>
    </button>
  </div>

  <!-- Liens de navigation -->
  <ul class="nav-links">
    <li>
      <a routerLink="/home">
        <fa-icon [icon]="faHome"></fa-icon>
        <span *ngIf="!isCollapsed">Accueil</span>
      </a>
    </li>
    <li *ngIf="hasRole(['ADMIN'])">
      <a routerLink="/dashboard">
        <fa-icon [icon]="faTachometerAlt"></fa-icon>
        <span *ngIf="!isCollapsed">Dashboard</span>
      </a>
    </li>

    <li *ngIf="hasRole(['ADMIN'])">
      <a routerLink="/users">
        <fa-icon [icon]="faUsers"></fa-icon>
        <span *ngIf="!isCollapsed">Liste des Utilisateurs</span>
      </a>
    </li>

    <li *ngIf="hasRole(['ADMIN','USER'])">
      <a routerLink="/produits">
        <fa-icon [icon]="faBox"></fa-icon>
        <span *ngIf="!isCollapsed">Liste des Produits</span>
      </a>
    </li>

    <li *ngIf="hasRole(['ADMIN'])">
      <a routerLink="/statistics">
        <fa-icon [icon]="faChartBar"></fa-icon>
        <span *ngIf="!isCollapsed">Statistiques</span>
      </a>
    </li>

    <li *ngIf="hasRole(['ADMIN'])">
      <a routerLink="/orders">
        <fa-icon [icon]="faList"></fa-icon>
        <span *ngIf="!isCollapsed">Liste des Commandes</span>
      </a>
    </li>

    <li *ngIf="hasRole(['USER', 'ADMIN'])">
      <a routerLink="/mes-commandes">
        <fa-icon [icon]="faList"></fa-icon>
        <span *ngIf="!isCollapsed">Mes Commandes</span>
      </a>
    </li>

    <li>
      <a routerLink="/about-us">
        <fa-icon [icon]="faInfoCircle"></fa-icon>
        <span *ngIf="!isCollapsed">À propos</span>
      </a>
    </li>

    <li *ngIf="hasRole(['USER', 'ADMIN'])">
      <a routerLink="/profile">
        <fa-icon [icon]="faUser"></fa-icon>
        <span *ngIf="!isCollapsed">Profil</span>
      </a>
    </li>

    <li *ngIf="hasRole(['USER', 'ADMIN'])">
      <a (click)="logout()">
        <fa-icon [icon]="faSignOutAlt"></fa-icon>
        <span *ngIf="!isCollapsed">Déconnexion</span>
      </a>
    </li>
  </ul>
</div>
