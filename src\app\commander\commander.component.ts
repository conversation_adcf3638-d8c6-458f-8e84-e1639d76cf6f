import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-commander',
  templateUrl: './commander.component.html',
  styleUrls: ['./commander.component.scss']
})
export class CommanderComponent implements OnInit {
  existingAddress: string = "123, Avenue Habib Bourguiba, Tunis";
  newAddress: string = "";
  confirmExistingAddress: boolean = false;
  enterNewAddress: boolean = false;
  
  // Variables pour les alertes attirantes
  showAlert = false;
  alertMessage = '';
  alertType = 'warning'; // 'success', 'warning', 'error', 'info'
  alertTitle = '';

  constructor(private router: Router) {}

  ngOnInit(): void {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('jwt');
      if (!token) {
        this.showCustomAlert(
          '🔐 Connexion requise !',
          "Veuillez vous connecter pour passer une commande. Vous allez être redirigé vers la page d'accueil.",
          'warning'
        );
        return;
      }
    }
  }

  toggleAddressOptions() {
    if (this.confirmExistingAddress) {
      this.enterNewAddress = false;
      this.newAddress = "";
    }
    if (this.enterNewAddress) {
      this.confirmExistingAddress = false;
    }
  }

  goToPayment() {
    if (!this.confirmExistingAddress && !this.newAddress) {
      this.showCustomAlert(
        '📍 Adresse manquante !',
        "Veuillez confirmer votre adresse existante ou entrer une nouvelle adresse pour continuer.",
        'warning'
      );
      return;
    }
    this.router.navigate(['/paiement']);
  }

  submitOrder() {
    let finalAddress = "";

    if (this.confirmExistingAddress) {
      finalAddress = this.existingAddress;
    } else if (this.enterNewAddress && this.newAddress.trim() !== "") {
      finalAddress = this.newAddress;
    } else {
      this.showCustomAlert(
        '⚠️ Adresse requise !',
        "Veuillez confirmer l'adresse existante ou entrer une nouvelle adresse valide.",
        'error'
      );
      return;
    }

    // Stocker l'adresse pour la page de paiement
    localStorage.setItem("deliveryAddress", finalAddress);

    // Alerte de succès avant redirection
    this.showCustomAlert(
      '✅ Commande confirmée !',
      `Votre adresse de livraison a été enregistrée. Redirection vers le paiement...`,
      'success'
    );
  }

  showCustomAlert(title: string, message: string, type: string = 'info'): void {
    this.alertTitle = title;
    this.alertMessage = message;
    this.alertType = type;
    this.showAlert = true;
  }

  closeAlert(): void {
    this.showAlert = false;
    
    // Actions spécifiques selon le type d'alerte
    if (this.alertType === 'warning' && this.alertMessage.includes('connecter')) {
      // Rediriger après fermeture de l'alerte de connexion
      setTimeout(() => {
        this.router.navigate(['/']);
      }, 300);
    } else if (this.alertType === 'success' && this.alertMessage.includes('Redirection')) {
      // Rediriger vers le paiement après confirmation
      setTimeout(() => {
        this.router.navigate(['/paiement']);
      }, 500);
    }
  }
}