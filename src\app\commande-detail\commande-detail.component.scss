.commande-detail-container {
  max-width: 700px;
  margin: 30px auto;
  padding: 30px 25px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #222;
  text-align: left;
  min-height: 70vh;
}

.commande-detail-container h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 25px;
  color: #007bff;
  text-align: center;
  text-shadow: 1px 1px 3px rgba(0,0,0,0.1);
}

.order-info p {
  font-size: 1.1rem;
  margin: 10px 0;
  color: #333;
}

h3 {
  margin-top: 30px;
  margin-bottom: 15px;
  font-size: 1.5rem;
  color: #007bff;
  text-align: center;
}

.order-items-list {
  list-style: none;
  padding: 0;
  margin: 0 0 30px 0;
}

.order-item {
  background: #f9faff;
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.order-item p {
  margin: 6px 0;
  font-size: 1rem;
  color: #444;
}

.btn-retour {
  display: block;
  width: 120px;
  margin: 0 auto;
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-retour:hover {
  background-color: #0056b3;
}
// Overlay pour l'alerte
.alert-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

// Alerte personnalisée
.custom-alert {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  min-width: 400px;
  max-width: 500px;
  margin: 20px;
  animation: slideUp 0.3s ease-out;
  overflow: hidden;
  border-left: 6px solid;

  &.alert-warning {
    border-left-color: #ff9800;
    .alert-header {
      background: linear-gradient(135deg, #ff9800, #ffb74d);
    }
    .btn-primary {
      background: linear-gradient(135deg, #ff9800, #ffb74d);
    }
  }

  &.alert-error {
    border-left-color: #f44336;
    .alert-header {
      background: linear-gradient(135deg, #f44336, #ef5350);
    }
    .btn-primary {
      background: linear-gradient(135deg, #f44336, #ef5350);
    }
  }

  &.alert-success {
    border-left-color: #4caf50;
    .alert-header {
      background: linear-gradient(135deg, #4caf50, #66bb6a);
    }
    .btn-primary {
      background: linear-gradient(135deg, #4caf50, #66bb6a);
    }
  }

  &.alert-info {
    border-left-color: #2196f3;
    .alert-header {
      background: linear-gradient(135deg, #2196f3, #42a5f5);
    }
    .btn-primary {
      background: linear-gradient(135deg, #2196f3, #42a5f5);
    }
  }
}

.alert-header {
  padding: 20px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  .alert-icon {
    font-size: 24px;
    margin-right: 10px;
  }

  .close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }
}

.alert-content {
  padding: 24px;
  
  p {
    margin: 0;
    font-size: 16px;
    line-height: 1.5;
    color: #333;
    text-align: center;
  }
}

.alert-actions {
  padding: 0 24px 24px;
  display: flex;
  justify-content: center;

  .btn-primary {
    border: none;
    color: white;
    padding: 12px 32px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

// Effet de flou sur le contenu en arrière-plan
.blur-background {
  filter: blur(3px);
  transition: filter 0.3s ease;
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// Spinner de chargement
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  p {
    color: #666;
    font-size: 16px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Bouton retour
.back-btn {
  background: none;
  border: 2px solid #3498db;
  color: #3498db;
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  margin-bottom: 20px;
  transition: all 0.3s ease;

  &:hover {
    background: #3498db;
    color: white;
    transform: translateX(-3px);
  }
}

// Style responsive
@media (max-width: 768px) {
  .custom-alert {
    min-width: auto;
    max-width: 90%;
    margin: 10px;
  }

  .alert-content p {
    font-size: 14px;
  }

  .btn-primary {
    padding: 10px 24px !important;
    font-size: 14px !important;
  }
}