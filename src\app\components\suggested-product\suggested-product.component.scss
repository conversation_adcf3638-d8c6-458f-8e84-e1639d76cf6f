.products-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.product-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem;
  width: 300px;
  box-shadow: 0 2px 5px rgb(0 0 0 / 0.1);
}

.product-image {
  width: 100%;
  height: auto;
  border-radius: 4px;
  object-fit: cover;
  margin-bottom: 0.5rem;
}

.product-name {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  color: #333;
}

.promotion {
  color: red;
  font-weight: bold;
}

.loading, .error, .no-products {
  margin: 1rem 0;
  font-weight: bold;
  text-align: center;
}
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
