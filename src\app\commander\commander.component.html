<!-- Alerte personnalisée -->
<div *ngIf="showAlert" class="alert-overlay" (click)="closeAlert()">
  <div class="custom-alert" [class]="'alert-' + alertType" (click)="$event.stopPropagation()">
    <div class="alert-header">
      <div class="alert-title">
        <span class="alert-icon">
          <span *ngIf="alertType === 'warning'">⚠️</span>
          <span *ngIf="alertType === 'error'">❌</span>
          <span *ngIf="alertType === 'success'">✅</span>
          <span *ngIf="alertType === 'info'">ℹ️</span>
        </span>
        <h3>{{ alertTitle }}</h3>
      </div>
      <button class="close-btn" (click)="closeAlert()">×</button>
    </div>
    <div class="alert-content">
      <p>{{ alertMessage }}</p>
    </div>
    <div class="alert-actions">
      <button class="btn-primary" (click)="closeAlert()">
        <span *ngIf="alertType === 'warning' && alertMessage.includes('connecter')">Se connecter</span>
        <span *ngIf="alertType === 'success'">Continuer</span>
        <span *ngIf="!(alertType === 'warning' && alertMessage.includes('connecter')) && alertType !== 'success'">Compris</span>
      </button>
    </div>
  </div>
</div>

<!-- Contenu principal -->
<div class="commande-card" [class.blur-background]="showAlert">
  <h2>Passer une commande</h2>
  
  <div class="address-section">
    <h3>Adresse de livraison</h3>
    
    <!-- Adresse existante -->
    <div class="address-option">
      <div class="adresse-existante">
        {{ existingAddress }}
      </div>
      <div class="checkbox-container">
        <input 
          type="checkbox" 
          id="confirmExisting" 
          [(ngModel)]="confirmExistingAddress"
          (change)="toggleAddressOptions()"
        >
        <label for="confirmExisting">Confirmer cette adresse</label>
      </div>
    </div>

    <!-- Nouvelle adresse -->
    <div class="address-option">
      <div class="checkbox-container">
        <input 
          type="checkbox" 
          id="newAddress" 
          [(ngModel)]="enterNewAddress"
          (change)="toggleAddressOptions()"
        >
        <label for="newAddress">Entrer une nouvelle adresse</label>
      </div>
      
      <div *ngIf="enterNewAddress" class="new-address-input">
        <input 
          type="text" 
          class="input-field" 
          placeholder="Entrez votre nouvelle adresse..."
          [(ngModel)]="newAddress"
        >
      </div>
    </div>
  </div>

  <button class="btn-commander" (click)="submitOrder()">
    Confirmer la commande
  </button>
</div>