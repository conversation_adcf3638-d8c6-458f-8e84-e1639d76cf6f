import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FavoriteService } from '../services/favorite.service';
import { FavoriteItem } from '../model/favorite-item.model';
import { Product } from '../model/produit.model';
import { ToastNotificationService } from '../services/toast-notification.service';
import { CartService } from '../services/cart.service';
import { CartItem } from '../model/cart-item.model';

@Component({
  selector: 'app-product-detail',
  templateUrl: './product-detail.component.html',
  styleUrl: './product-detail.component.scss'
})
export class ProductDetailComponent implements OnInit{
   constructor(
      
      private favoriteService: FavoriteService,
      private toastService: ToastNotificationService,
          private cartService: CartService,
      
  
    ) {}
  @Input() product: any;
  @Output() close = new EventEmitter<void>();
  currentImageIndex: number = 0;
  currentImage: string = '';

  ngOnInit() {
    this.initializeImages();
  }
    

  addToFavorites(produit: Product) {
   const prix = isNaN(Number(produit.prixPromotion)) || !produit.prixPromotion
  ? Number(produit.prix)
  : Number(produit.prixPromotion);

  const item: FavoriteItem = {
    productId: produit.idp!,
    nom: produit.nom,
        quantity: 1, // quantité initiale

    prix: prix,
            images: produit.imageUrl,


  };
  this.favoriteService.addToFavorites(item);
this.toastService.showSuccess(
  `${produit.nom}`,
  'a été ajouté aux favoris ✅'
);
  }

   ngOnChanges() {
    this.initializeImages();
  }

  // Initialiser les images
  initializeImages() {
    if (this.product) {
      // Si le produit a un tableau d'images
      if (this.product.imageUrl && this.product.imageUrl.length > 0) {
        this.currentImage = this.product.imageUrl[0];
        this.currentImageIndex = 0;
      } 
      // Sinon utiliser l'image principale (rétrocompatibilité)
      else if (this.product.imageUrl) {
        this.currentImage = this.product.imageUrl;
        this.currentImageIndex = 0;
        // Créer un tableau avec une seule image pour la cohérence
        this.product.imageUrl = [this.product.imageUrl];
      }
    }
  }

  // Naviguer vers l'image suivante
  nextImage() {
    if (this.product.imageUrl && this.product.imageUrl.length > 1) {
      this.currentImageIndex = (this.currentImageIndex + 1) % this.product.imageUrl.length;
      this.currentImage = this.product.imageUrl[this.currentImageIndex];
    }
  }

  // Naviguer vers l'image précédente
  previousImage() {
    if (this.product.imageUrl && this.product.imageUrl.length > 1) {
      this.currentImageIndex = this.currentImageIndex === 0 
        ? this.product.imageUrl.length - 1 
        : this.currentImageIndex - 1;
      this.currentImage = this.product.imageUrl[this.currentImageIndex];
    }
  }

  // Définir l'image courante par index
  setCurrentImage(index: number) {
    if (this.product.imageUrl && index >= 0 && index < this.product.imageUrl.length) {
      this.currentImageIndex = index;
      this.currentImage = this.product.imageUrl[index];
    }
  }
   addToCart(produit: Product) {
      const item: CartItem = {
        productId: produit.idp!,
        nom: produit.nom,
        prix: produit.prix,
        quantity: 1,
        images: produit.imageUrl
      };
      this.cartService.addToCart(item);
    this.toastService.showSuccess(
          'Ajouté au panier ! 🛒',
          `${produit.nom} a été ajouté à votre panier avec succès`
        );  }
}