<nav class="bg-gray-800 text-white p-4 flex justify-center items-center relative h-20">
  <!-- Message centré -->
  <div class="text-lg font-bold text-center fade-text absolute left-1/2 transform -translate-x-1/2">
    Me<PERSON><PERSON> de <PERSON>'amuser avec notre IA 😊
  </div>

  <!-- Favoris + Panier + Connexion / DisplayName -->
  <div class="absolute right-4 flex items-center gap-4">
    <!-- Icône des favoris -->
    <img
      src="assets/5323696.png"
      alt="Icône des favoris"
      class="w-8 h-8 cursor-pointer hover:opacity-80 transition-opacity"
      (click)="toggleFavorites()"
    />

    <!-- Icône du panier -->
    <img
      src="assets/5993968.png"
      alt="Icône du panier"
      class="w-8 h-8 cursor-pointer hover:opacity-80 transition-opacity"
      (click)="toggleCart()"
    />

    <!-- Si pas connecté -->
    <ng-container *ngIf="!isLoggedIn; else loggedInBlock">
      <button (click)="openModal()" class="bg-blue-500 px-4 py-2 rounded hover:bg-blue-600 transition-colors">
        Connexion
      </button>
    </ng-container>

    <!-- Si connecté -->
    <ng-template #loggedInBlock>
      <div class="relative">
        <button (click)="toggleMenu()" class="flex items-center gap-2 bg-blue-500 px-4 py-2 rounded hover:bg-blue-600 transition-colors">
          {{ userDisplayName }}
          <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        <!-- Menu déroulant -->
        <div *ngIf="isMenuOpen" class="absolute right-0 mt-2 w-48 bg-white text-black rounded shadow-lg z-10">
          <a routerLink="/profile" (click)="closeMenu()" class="block px-4 py-2 hover:bg-gray-100 text-black no-underline">Gérer Profil</a>
          <a routerLink="/mes-commandes" (click)="closeMenu()" class="block px-4 py-2 hover:bg-gray-100 text-black no-underline">Mes Commandes</a>
          <button (click)="logout(); closeMenu()" class="block w-full text-left px-4 py-2 hover:bg-gray-100 text-black">Déconnexion</button>
        </div>
      </div>
    </ng-template>
  </div>
</nav>

<!-- 🛑 Modal de Connexion -->
<div *ngIf="isModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center transition-opacity duration-300" (click)="closeModal()">
  <div class="bg-white text-black p-5 rounded-lg w-96 transform transition-transform duration-300 scale-95" (click)="$event.stopPropagation()">
      <h2 class="text-xl mb-2">Connexion</h2>

      <!-- Champ Email avec label -->
      <label for="email" class="block text-sm mb-1">Email</label>
      <input type="email" id="email" placeholder="Email" class="border p-2 w-full my-2" [(ngModel)]="email" />

      <!-- Champ Mot de passe avec label -->
      <label for="password" class="block text-sm mb-1">Mot de passe</label>
      <input type="password" id="password" placeholder="Mot de passe" class="border p-2 w-full my-2" [(ngModel)]="password" />

      <!-- Lien "Mot de passe oublié" -->
      <a href="#" class="text-blue-500 text-sm block text-right mb-4 hover:text-blue-600 transition-colors">
          Mot de passe oublié ?
      </a>

      <!-- Bouton "Se connecter" -->
      <button class="bg-blue-500 text-white p-2 w-full hover:bg-blue-600 transition-colors" (click)="login()">
          Se connecter
      </button>

      <!-- Bouton "Annuler" -->
      <button class="bg-gray-300 text-black p-2 w-full mt-2 hover:bg-gray-400 transition-colors" (click)="closeModal()">
          Annuler
      </button>

      <!-- Lien "Créer un compte" -->
      <p class="text-center mt-4">
          Pas de compte ?
          <a routerLink="/register" (click)="closeModal()" class="text-blue-500 hover:text-blue-600 transition-colors">
              Créer un compte
          </a>
      </p>
  </div>
</div>

<!-- Modal des Favoris -->
<div *ngIf="isFavoritesOpen" class="cart-backdrop" (click)="toggleFavorites()">
  <div class="cart-modal" (click)="$event.stopPropagation()">
    <div class="cart-content">
      <h2 class="cart-title">Mes Favoris</h2>
      
      <!-- Message si pas de favoris -->
      <div *ngIf="favoriteItems.length === 0" class="text-center py-8">
        <p class="text-gray-500 mb-4">Aucun article dans vos favoris</p>
        <button class="close-button" (click)="toggleFavorites()">Fermer</button>
      </div>

      <!-- Liste des favoris -->
      <div *ngIf="favoriteItems.length > 0">
        <div class="favorites-grid">
          <div *ngFor="let item of favoriteItems" class="favorite-item">
<img *ngIf="item.images?.[0]" [src]="item.images[0]" alt="{{item.nom}}" width="100" />

              <h3 class="favorite-name">{{ item.nom }}</h3>
              <p class="favorite-price">{{ item.prix}} TND</p>
              <div class="favorite-actions">
                <button class="add-to-cart-button" (click)="addToCart(item)">
                  Ajouter au panier
                </button>
                <button class="remove-favorite-button" (click)="removeFromFavorites(item)">
                  Retirer des favoris
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div class="cart-footer">
          <button class="close-button" (click)="toggleFavorites()">Fermer</button>
        </div>
      </div>
    </div>
  </div>


<!-- Modal du Panier -->
<div *ngIf="isCartOpen" class="cart-backdrop" (click)="toggleCart()">
  <div class="cart-modal" (click)="$event.stopPropagation()">
    <div class="cart-content">
      <h2 class="cart-title">Mon Panier</h2>
      <!-- Tableau des articles -->
      <table class="cart-table">
        <thead>
          <tr>
            <th>images</th>
            <th>Article</th>
            <th>Prix Unitaire</th>
            <th>Quantité</th>
            <th>Total</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of cartItems">
             <td>
      <img [src]="item.images[0]" alt="{{ item.nom }}" width="80" height="80" /></td>
            <td>{{ item.nom }}</td>
            <td>{{ item.prix }} € </td>
            <td>
              <input
                type="number"
                [(ngModel)]="item.quantity"
                min="1"
                class="quantity-input"
                (change)="updateQuantity(item)"
              />
            </td>
            <td>{{ item.prix * item.quantity }} TND</td>
            <td>
              <button class="delete-button" (click)="removeItem(item)">Supprimer</button>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Total et boutons -->
      <div class="cart-footer">
        <span class="total-amount">Total: {{ amount }} TND</span>
        <div class="buttons-container">
          <button class="checkout-button" (click)="goToCommande()">Passer la Commande</button>
          <button class="close-button" (click)="toggleCart()">Fermer</button>
        </div>
      </div>
    </div>
  </div>
</div>