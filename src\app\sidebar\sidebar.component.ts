import { Component, OnInit } from '@angular/core';

import { faBars, faBox ,faList, faUsers, faChartBar, faInfoCircle, faUser, faSignOutAlt, faHome, faTachometerAlt } from '@fortawesome/free-solid-svg-icons';

import { AuthService } from '../services/auth.service';  // Service d'authentification
import { ToastNotificationService } from '../services/toast-notification.service';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent implements OnInit {
  isCollapsed = false; // Variable pour gérer l'état du menu
  faBars = faBars;
  faList = faList;
  faUsers = faUsers;
  faChartBar = faChartBar;
  faInfoCircle = faInfoCircle;
  faUser = faUser;
  faSignOutAlt = faSignOutAlt;
  faHome = faHome;
  faTachometerAlt = faTachometerAlt;

   faBox = faBox;





  role: string | null = null; // Contiendra l'utilisateur connecté

  constructor(private authService: AuthService,        private toastService: ToastNotificationService
  ) {}

  ngOnInit(): void {
    // Vérifier si l'environnement est un navigateur et que localStorage est disponible
    if (typeof window !== 'undefined' && window.localStorage) {
      // Vérifier si l'utilisateur est connecté et récupérer ses informations
      this.role = localStorage.getItem('role');
    } else {
      console.warn('localStorage n\'est pas disponible dans cet environnement');
    }
  }
  
  hasRole(rolesToCheck: string[]): boolean {
    return rolesToCheck.some(role => this.role?.toLowerCase() === role.toLowerCase());
  }
  
  toggleSidebar() {
    this.isCollapsed = !this.isCollapsed; // Change l'état du menu
  }

  logout() {
    this.authService.logout();
    this.toastService.showSuccess(
          'Déconnexion ',
          'réussie ! 👋'
        );  // Assurez-vous d'avoir une méthode de déconnexion dans votre service d'authentification
    console.log("Déconnexion...");
    window.location.href = '/home';
  }}
