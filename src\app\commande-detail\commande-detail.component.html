<!-- Alerte personnalisée -->
<div *ngIf="showAlert" class="alert-overlay" (click)="closeAlert()">
  <div class="custom-alert" [class]="'alert-' + alertType" (click)="$event.stopPropagation()">
    <div class="alert-header">
      <div class="alert-icon">
        <span *ngIf="alertType === 'warning'">⚠️</span>
        <span *ngIf="alertType === 'error'">❌</span>
        <span *ngIf="alertType === 'success'">✅</span>
        <span *ngIf="alertType === 'info'">ℹ️</span>
      </div>
      <button class="close-btn" (click)="closeAlert()">×</button>
    </div>
    <div class="alert-content">
      <p>{{ alertMessage }}</p>
    </div>
    <div class="alert-actions">
      <button class="btn-primary" (click)="closeAlert()">
        <span *ngIf="alertType === 'warning' && alertMessage.includes('Authentification')">Se connecter</span>
        <span *ngIf="!(alertType === 'warning' && alertMessage.includes('Authentification'))">OK</span>
      </button>
    </div>
  </div>
</div>

<!-- Contenu principal existant -->
<div [class.blur-background]="showAlert">
  <!-- Template de chargement -->
  <ng-template #loadingTpl>
    <div class="loading-spinner">
      <div class="spinner"></div>
      <p *ngIf="loading">Chargement des détails de votre commande...</p>
      <p *ngIf="error">{{ error }}</p>
    </div>
  </ng-template>

  <!-- Contenu principal de la commande -->
  <div class="commande-detail-container" *ngIf="!loading && order; else loadingTpl">
    <h2>Détails de la commande</h2>
    
    <div class="order-info">
      <p><strong>ID commande :</strong> {{ order.id }}</p>
      <p><strong>Date :</strong> {{ order.orderDate | date:'dd/MM/yyyy HH:mm' }}</p>
      <p><strong>Statut :</strong> {{ order.status }}</p>
      <p><strong>Prix total :</strong> {{ order.totalPrice | currency:'EUR' }}</p>
    </div>

    <h3>Articles</h3>
    <ul class="order-items-list">
      <li *ngFor="let item of order.items" class="order-item">
        <p><strong>Nom :</strong> {{ item.product.nom }}</p>
        <p><strong>Caractéristiques :</strong> {{ item.product.caracteristiques }}</p>
        <p><strong>Catégorie :</strong> {{ item.product.categorie }}</p>
        <p><strong>Couleur :</strong> {{ item.product.couleur.join(', ') }}</p>
        <p><strong>Prix :</strong> {{ item.price | currency:'EUR' }}</p>
        <p><strong>Quantité :</strong> {{ item.quantity }}</p>
      </li>
    </ul>

    <button class="btn-retour" (click)="goBack()">Retour</button>
  </div>
</div>