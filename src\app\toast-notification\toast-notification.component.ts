// toast-notification.component.ts
import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { ToastNotification, ToastNotificationService } from '../services/toast-notification.service';

@Component({
  selector: 'app-toast-notification',
  template: `
    <div class="toast-container">
      <div 
        *ngFor="let toast of toasts; trackBy: trackByToastId" 
        class="toast toast-{{toast.type}}"
        [@slideInOut]
      >
        <div class="toast-icon">
          {{ toast.icon }}
        </div>
        <div class="toast-content">
          <div class="toast-title">{{ toast.title }}</div>
          <div class="toast-message">{{ toast.message }}</div>
        </div>
        <button 
          class="toast-close" 
          (click)="removeToast(toast.id)"
          aria-label="Fermer"
        >
          ✕
        </button>
      </div>
    </div>
  `,
  styleUrls: ['./toast-notification.component.scss'],
  animations: [
    // Vous pouvez ajouter des animations Angular ici si nécessaire
  ]
})
export class ToastNotificationComponent implements OnInit, OnDestroy {
  toasts: ToastNotification[] = [];
  private subscription?: Subscription;

  constructor(private toastService: ToastNotificationService) {}

  ngOnInit() {
    this.subscription = this.toastService.toasts$.subscribe(
      toasts => this.toasts = toasts
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  removeToast(id: string) {
    this.toastService.removeToast(id);
  }

  trackByToastId(index: number, toast: ToastNotification): string {
    return toast.id;
  }
}