import { Injectable } from '@angular/core';
import { FavoriteItem } from '../model/favorite-item.model';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class FavoriteService {
  private favoritesKey = 'favorites';
  private favorites: FavoriteItem[] = [];
  private favoritesSubject = new BehaviorSubject<FavoriteItem[]>([]);

  constructor() {
    this.loadFavoritesFromStorage();
  }

  private isLocalStorageAvailable(): boolean {
    try {
      return typeof localStorage !== 'undefined' && localStorage !== null;
    } catch {
      return false;
    }
  }

  private loadFavoritesFromStorage(): void {
    if (!this.isLocalStorageAvailable()) {
      console.warn('localStorage n’est pas disponible.');
      this.favorites = [];
      this.favoritesSubject.next(this.favorites);
      return;
    }

    try {
      const data = localStorage.getItem(this.favoritesKey);
      if (data) {
        this.favorites = JSON.parse(data);
        if (!Array.isArray(this.favorites)) {
          this.favorites = [];
        }
      } else {
        this.favorites = [];
      }
    } catch (error) {
      console.error('Erreur de chargement des favoris:', error);
      this.favorites = [];
    }
    this.favoritesSubject.next(this.favorites);
  }

  private saveFavoritesToStorage(): void {
    if (!this.isLocalStorageAvailable()) {
      console.warn('localStorage n’est pas disponible, impossible de sauvegarder.');
      return;
    }

    localStorage.setItem(this.favoritesKey, JSON.stringify(this.favorites));
  }

  getFavorites() {
    return this.favoritesSubject.asObservable();
  }

  addToFavorites(item: FavoriteItem): void {
    const exists = this.favorites.some(fav => fav.productId === item.productId);
    if (!exists) {
      this.favorites.push(item);
      this.saveFavoritesToStorage();
      this.favoritesSubject.next(this.favorites);
    }
  }

  removeFromFavorites(productId: string): void {
    this.favorites = this.favorites.filter(item => item.productId !== productId);
    this.saveFavoritesToStorage();
    this.favoritesSubject.next(this.favorites);
  }

  isFavorite(productId: string): boolean {
    return this.favorites.some(item => item.productId === productId);
  }

  clearFavorites(): void {
    this.favorites = [];
    this.saveFavoritesToStorage();
    this.favoritesSubject.next(this.favorites);
  }
}
