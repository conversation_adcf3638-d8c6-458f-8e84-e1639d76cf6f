/* You can add global styles to this file, and also import other style files */
@tailwind base;
@tailwind components;
@tailwind utilities;
html, body { height: 100%; 
 background-color: #111827;
 margin: 0;}
body { margin: 0; font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif; }

@keyframes fadeInOut {
    0% {
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
  
  .fade-text {
    animation: fadeInOut 5s infinite ease-in-out;
  }
  
 


/* Styles CSS pour un toastr attirant */

/* Container principal du toast centré */
.toast-center-center {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  width: auto !important;
  min-width: 350px !important;
  max-width: 500px !important;
  z-index: 999999 !important;
}

/* Styles généraux du toast */
.toast-center-center .toast {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 16px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  padding: 20px 24px !important;
  box-shadow: 
    0 20px 40px rgba(102, 126, 234, 0.3),
    0 8px 16px rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(10px) !important;
  position: relative !important;
  overflow: hidden !important;
  animation: slideInScale 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards !important;
}

/* Toast de succès */
.toast-center-center .toast-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
  box-shadow: 
    0 20px 40px rgba(79, 172, 254, 0.3),
    0 8px 16px rgba(0, 0, 0, 0.15) !important;
}

/* Toast d'erreur */
.toast-center-center .toast-error {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important;
  box-shadow: 
    0 20px 40px rgba(250, 112, 154, 0.3),
    0 8px 16px rgba(0, 0, 0, 0.15) !important;
}

/* Toast d'avertissement */
.toast-center-center .toast-warning {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%) !important;
  color: #2d3436 !important;
  box-shadow: 
    0 20px 40px rgba(255, 234, 167, 0.3),
    0 8px 16px rgba(0, 0, 0, 0.15) !important;
}

/* Toast d'information */
.toast-center-center .toast-info {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%) !important;
  box-shadow: 
    0 20px 40px rgba(116, 185, 255, 0.3),
    0 8px 16px rgba(0, 0, 0, 0.15) !important;
}

/* Titre du toast */
.toast-center-center .toast-title {
  font-weight: 700 !important;
  font-size: 16px !important;
  margin-bottom: 8px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

/* Message du toast */
.toast-center-center .toast-message {
  opacity: 0.95 !important;
  line-height: 1.6 !important;
}

/* Bouton de fermeture */
.toast-center-center .toast-close-button {
  position: absolute !important;
  top: 12px !important;
  right: 12px !important;
  background: rgba(255, 255, 255, 0.2) !important;
  border: none !important;
  border-radius: 50% !important;
  width: 28px !important;
  height: 28px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  color: white !important;
  font-size: 16px !important;
  font-weight: bold !important;
}

.toast-center-center .toast-close-button:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: scale(1.1) !important;
}

/* Barre de progression */
.toast-center-center .toast-progress {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  height: 4px !important;
  background: rgba(255, 255, 255, 0.3) !important;
  border-radius: 0 0 16px 16px !important;
}

/* Effet de brillance */
.toast-center-center .toast::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  ) !important;
  animation: shine 2s infinite !important;
}

/* Animations */
@keyframes slideInScale {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.7) rotateY(45deg);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.05) rotateY(0deg);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotateY(0deg);
  }
}

@keyframes shine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Animation de sortie */
.toast-center-center .toast.ng-animating {
  animation: slideOutScale 0.3s ease-in forwards !important;
}

@keyframes slideOutScale {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8) translateY(-20px);
  }
}

/* Effet hover sur le toast */
.toast-center-center .toast:hover {
  transform: translate(-50%, -50%) scale(1.02) !important;
  transition: transform 0.2s ease !important;
}

/* Responsive */
@media (max-width: 480px) {
  .toast-center-center {
    min-width: 280px !important;
    max-width: 90vw !important;
  }
  
  .toast-center-center .toast {
    padding: 16px 20px !important;
    font-size: 13px !important;
  }
  
  .toast-center-center .toast-title {
    font-size: 14px !important;
  }
}