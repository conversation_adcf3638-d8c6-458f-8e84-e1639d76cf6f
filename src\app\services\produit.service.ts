import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Product } from '../model/produit.model';


@Injectable({
  providedIn: 'root'
})
export class ProduitService {
  private apiUrl = 'http://localhost:8082/api/produits'; // Adjust API URL

  constructor(private http: HttpClient) { }

  getProduits(): Observable<Product[]> {
    return this.http.get<Product[]>(this.apiUrl);
  }



  createProduit(Produit: Product): Observable<Product> {
    return this.http.post<Product>(this.apiUrl, Produit);
  }

  updateProduit(id: string, Produit: Product): Observable<Product> {
    return this.http.put<Product>(`${this.apiUrl}/${id}`, Produit);
  }

  deleteProduit(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  getProduitById(id: string): Observable<Product> {
    return this.http.get<Product>(`${this.apiUrl}/id/${id}`);
  }
  extractAllProductNamesFromHistory(): string[] {
  const historyStr = localStorage.getItem('products_history');
  if (!historyStr) return [];

  try {
    const history = JSON.parse(historyStr); // tableau d'objets avec "products"
    const allNames: string[] = [];

    history.forEach((session: any) => {
      if (Array.isArray(session.products)) {
        session.products.forEach((product: any) => {
          if (product.nom) {
            allNames.push(product.nom);
          }
        });
      }
    });

    return allNames;
  } catch (err) {
    console.error('Erreur lors du parsing de products_history:', err);
    return [];
  }
}

getProductsByName(name: string): Observable<Product[]> {
  return this.http.get<Product[]>(`http://localhost:8082/api/produits/products/by-name?name=${name}`);
}
  addAvis(productId: string, avisCommentaire: string): Observable<string> {
    const url = `${this.apiUrl}/${productId}/addAvis`;
    const body = { avisCommentaire };
    return this.http.post<string>(url, body);
  }
}
