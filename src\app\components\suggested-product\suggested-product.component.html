<!-- Wrapper avec scroll vertical -->
<div class="h-[80vh] overflow-y-auto p-4 bg-gray-50 rounded-lg">

  <!-- Bouton ouvrir l'historique -->
  <button (click)="openHistoryModal()" 
          class="mb-6 px-4 py-2 bg-indigo-600 text-white rounded-lg shadow hover:bg-indigo-700 transition">
    Historique des recherches
  </button>

  <!-- Section Produits Suggérés -->
  <div class="h-full">
    <!-- Header -->
    <div class="mb-6">
      <h2 class="text-3xl font-bold text-gray-800 mb-2 flex items-center">
        <i class="fas fa-star text-yellow-500 mr-3"></i>
        Produits Suggérés
      </h2>
      <p class="text-gray-600">Découvrez notre sélection personnalisée pour vous</p>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="flex items-center justify-center py-12">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-600">Chargement des produits...</p>
      </div>
    </div>

    <!-- Error State -->
    <div *ngIf="error && !loading" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex items-center">
        <i class="fas fa-exclamation-triangle text-red-500 mr-3"></i>
        <p class="text-red-700 font-medium">{{ error }}</p>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="!loading && !error && allProducts.length === 0" class="text-center py-12">
      <div class="bg-gray-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-4">
        <i class="fas fa-shopping-bag text-4xl text-gray-400"></i>
      </div>
      <h3 class="text-xl font-semibold text-gray-600 mb-2">Aucun produit suggéré</h3>
      <p class="text-gray-500">Commencez à explorer pour voir des recommandations personnalisées</p>
    </div>

    <!-- Products Grid -->
   <div *ngIf="!loading && !error && allProducts.length > 0" 
     class="grid grid-cols-1 md:grid-cols-2 gap-6">

      <div *ngFor="let product of allProducts; let i = index" 
           class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group cursor-pointer transform hover:-translate-y-1">
        <!-- ... Ton code existant pour afficher chaque produit ... -->
        <!-- (Tu peux garder le code produit exactement comme tu l'as écrit) -->
        <!-- Product Image/Icon -->
        <div class="h-48 flex items-center justify-center relative"
             [ngClass]="{
               'bg-gradient-to-br from-blue-100 to-blue-200': i % 6 === 0,
               'bg-gradient-to-br from-purple-100 to-purple-200': i % 6 === 1,
               'bg-gradient-to-br from-green-100 to-green-200': i % 6 === 2,
               'bg-gradient-to-br from-orange-100 to-orange-200': i % 6 === 3,
               'bg-gradient-to-br from-red-100 to-red-200': i % 6 === 4,
               'bg-gradient-to-br from-indigo-100 to-indigo-200': i % 6 === 5
             }">
          <img *ngIf="product.imageUrl" 
               [src]="product.imageUrl[0]" 
               [alt]="product.nom"
               class="w-full h-full object-cover">
          <i *ngIf="!product.imageUrl" 
             class="fas fa-box text-4xl"
             [ngClass]="{
               'text-blue-600': i % 6 === 0,
               'text-purple-600': i % 6 === 1,
               'text-green-600': i % 6 === 2,
               'text-orange-600': i % 6 === 3,
               'text-red-600': i % 6 === 4,
               'text-indigo-600': i % 6 === 5
             }"></i>
          <div *ngIf="product.prixPromotion && product.prixPromotion < product.prix" 
               class="absolute top-3 right-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold shadow-lg">
            💸 PROMO
          </div>
        </div>
        <div class="p-5">
          <h3 class="text-xl font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors line-clamp-2">
            {{ product.nom }}
          </h3>
          <div class="flex items-center justify-between mb-3">
            <div class="flex flex-col">
              <span *ngIf="product.prixPromotion && product.prixPromotion < product.prix"
                    class="text-2xl font-bold text-green-600">
                {{ product.prixPromotion | currency:'EUR':'symbol':'1.2-2':'fr-FR' }}
              </span>
              <span [ngClass]="{
                      'text-2xl font-bold': !(product.prixPromotion && product.prixPromotion < product.prix),
                      'text-lg line-through text-gray-500': product.prixPromotion && product.prixPromotion < product.prix
                    }"
                    [class]="!(product.prixPromotion && product.prixPromotion < product.prix) ? 
                            (i % 6 === 0 ? 'text-blue-600' : 
                             i % 6 === 1 ? 'text-purple-600' : 
                             i % 6 === 2 ? 'text-green-600' : 
                             i % 6 === 3 ? 'text-orange-600' : 
                             i % 6 === 4 ? 'text-red-600' : 'text-indigo-600') : ''">
                {{ product.prix | currency:'EUR':'symbol':'1.2-2':'fr-FR' }}
              </span>
            </div>
            <span class="px-2 py-1 rounded-full text-xs font-semibold"
                  [ngClass]="{
                    'bg-blue-100 text-blue-800': i % 6 === 0,
                    'bg-purple-100 text-purple-800': i % 6 === 1,
                    'bg-green-100 text-green-800': i % 6 === 2,
                    'bg-orange-100 text-orange-800': i % 6 === 3,
                    'bg-red-100 text-red-800': i % 6 === 4,
                    'bg-indigo-100 text-indigo-800': i % 6 === 5
                  }">
              <span [ngSwitch]="i % 6">
                <span *ngSwitchCase="0">⭐ Populaire</span>
                <span *ngSwitchCase="1">🔥 Tendance</span>
                <span *ngSwitchCase="2">💎 Premium</span>
                <span *ngSwitchCase="3">🎯 Recommandé</span>
                <span *ngSwitchCase="4">⚡ Nouveau</span>
                <span *ngSwitchDefault>✨ Qualité</span>
              </span>
            </span>
          </div>
          <p *ngIf="product.description" 
             class="text-gray-600 text-sm mb-4 line-clamp-2">
            {{ product.description }}
          </p>
          <div class="flex gap-2 mt-auto">
            <button (click)="addToCart(product)" 
                    class="flex-1 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-200 hover:scale-105 shadow-md"
                    [ngClass]="{
                      'bg-blue-600 hover:bg-blue-700': i % 6 === 0,
                      'bg-purple-600 hover:bg-purple-700': i % 6 === 1,
                      'bg-green-600 hover:bg-green-700': i % 6 === 2,
                      'bg-orange-600 hover:bg-orange-700': i % 6 === 3,
                      'bg-red-600 hover:bg-red-700': i % 6 === 4,
                      'bg-indigo-600 hover:bg-indigo-700': i % 6 === 5
                    }">
              <i class="fas fa-shopping-cart mr-2"></i>
              Ajouter
            </button>
            <button (click)="showDetails(product)" 
                    class="bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm px-3 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-1 hover:scale-105">
              <i class="fas fa-eye text-sm"></i>
              <span>Voir plus</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Load More Button -->
    <div *ngIf="!loading && !error && allProducts.length >= 15" class="mt-8 flex justify-center">
      <button (click)="loadMore()" 
              class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition">
        Charger plus
      </button>
    </div>
  </div>
</div>

<!-- Modal Historique -->
<div *ngIf="isHistoryModalOpen" 
     class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-white rounded-lg max-w-lg w-full p-6 relative shadow-xl">
    <button (click)="closeHistoryModal()" 
            class="absolute top-4 right-4 text-gray-600 hover:text-gray-900 text-2xl font-bold">
      &times;
    </button>
    <h3 class="text-2xl font-bold mb-4 text-gray-800">Historique des produits recherchés</h3>

    <div *ngIf="historyProducts.length === 0" class="text-center text-gray-500 py-10">
      Aucun produit dans l'historique.
    </div>

    <ul *ngIf="historyProducts.length > 0" class="space-y-3 max-h-96 overflow-y-auto">
      <li *ngFor="let product of historyProducts" 
          class="p-3 border rounded-lg hover:bg-gray-100 cursor-pointer flex items-center space-x-4"
          (click)="showDetails(product); closeHistoryModal()">
        <img *ngIf="product.imageUrl.length > 0" 
             [src]="product.imageUrl[0]" alt="{{product.nom}}" 
             class="w-12 h-12 object-cover rounded-md">
        <i *ngIf="!product.imageUrl || product.imageUrl.length === 0" 
           class="fas fa-box text-gray-400 text-2xl"></i>
        <div>
          <p class="font-semibold text-gray-700">{{ product.nom }}</p>
          <p class="text-sm text-gray-500">{{ product.caracteristiques ? (product.caracteristiques | slice:0:50) + '...' : '' }}</p>
        </div>
      </li>
    </ul>
  </div>

</div> 
<app-product-detail 
  *ngIf="selectedProduct" 
  [product]="selectedProduct" 
  (close)="selectedProduct = null">
</app-product-detail>

