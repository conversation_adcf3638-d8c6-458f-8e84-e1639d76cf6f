<div class="user-details-container">
    <div class="card">
      <!-- Retour -->
      <a class="back-link" (click)="goBack()">← Retour</a>
  
      <!-- Détails utilisateur -->
      <h1>Détails de l'utilisateur</h1>
      
      <div class="user-details">
        <p><strong>Nom:</strong> <span class="detail">{{ selectedUser.displayName }}</span></p>
        <p><strong>Email:</strong> <span class="detail">{{ selectedUser.email }}</span></p>
        <p><strong>Numéro de téléphone:</strong> <span class="detail">{{ selectedUser.phoneNumber }}</span></p>
        <p><strong>Adresse:</strong> <span class="detail">{{ selectedUser.address }}</span></p>
        <p><strong>Rôle:</strong> <span class="detail">{{ selectedUser.role }}</span></p>
        <p><strong>Statut:</strong> <span class="detail">{{ selectedUser.archived ? 'Archivé' : 'Actif' }}</span></p>
      </div>
      
      <!-- Bouton Retour dans la carte -->
      <button class="return-btn" (click)="goBack()">Retour</button>
    </div>
  </div>
  