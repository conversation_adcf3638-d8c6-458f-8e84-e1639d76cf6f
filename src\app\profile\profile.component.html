<div class="profile-container" *ngIf="currentUser; else loading">
  <div class="profile-card">
    <h1>Mon Profil</h1>
    <p><strong>Nom utilisateur:</strong> {{ currentUser.displayName }}</p>
    <p><strong>Email:</strong> {{ currentUser.email }}</p>
    
<!-- Affichage ou formulaire de modification des coordonnées -->
<div *ngIf="isEditingCoordinates; else profileInfo">
  <form (ngSubmit)="saveCoordinates()">
    <label for="phoneNumber">Téléphone:</label>
    <input 
      id="phoneNumber" 
      type="text" 
      [(ngModel)]="editPhoneNumber" 
      name="phoneNumber" 
      required 
    />

    <label for="address">Adresse:</label>
    <input 
      id="address" 
      type="text" 
      [(ngModel)]="editAddress" 
      name="address" 
      required 
    />

    <div class="form-buttons">
      <button type="submit" class="save-btn">Enregistrer</button>
      <button type="button" class="cancel-btn" (click)="cancelEditingCoordinates()">Annuler</button>
    </div>
  </form>
</div>

<!-- Affichage des coordonnées -->
<ng-template #profileInfo>
  <p><strong>Téléphone:</strong> {{ currentUser!.phoneNumber }}</p>
  <p><strong>Adresse:</strong> {{ currentUser!.address }}</p>

  <button *ngIf="currentUser?.role !== 'ADMIN'" class="edit-btn" (click)="enableEditingCoordinates()">Modifier les coordonnées</button>
</ng-template>


    <!-- Affichage ou formulaire de modification du mot de passe -->
    <div *ngIf="isEditingPassword; else passwordInfo">
      <form (ngSubmit)="savePassword()">
        <label for="oldPassword">Ancien mot de passe:</label>
        <input id="oldPassword" type="password" [(ngModel)]="oldPassword" name="oldPassword" required />

        <label for="newPassword">Nouveau mot de passe:</label>
        <input id="newPassword" type="password" [(ngModel)]="newPassword" name="newPassword" required />

        <label for="confirmPassword">Confirmer le mot de passe:</label>
        <input id="confirmPassword" type="password" [(ngModel)]="confirmPassword" name="confirmPassword" required />

        <div class="form-buttons">
          <button type="submit" class="save-btn">Enregistrer</button>
          <button type="button" class="cancel-btn" (click)="cancelEditingPassword()">Annuler</button>
        </div>
        <div *ngIf="successMessage" class="text-green-500 mt-2">
          {{ successMessage }}
        </div>
        <div *ngIf="errorMessage" class="text-red-500 mt-2">
          {{ errorMessage }}
        </div>
      </form>
    </div>

    <!-- Affichage du mot de passe (ou un message à propos de la modification du mot de passe) -->
    <ng-template #passwordInfo>
      <button *ngIf="currentUser?.role !== 'ADMIN'" class="edit-btn" (click)="enableEditingPassword()">Modifier le mot de passe</button>
    </ng-template>

    <!-- Affichage des messages de succès ou d'erreur -->
 

  </div>
</div>

<ng-template #loading>
  <p>Chargement du profil...</p>
</ng-template>

