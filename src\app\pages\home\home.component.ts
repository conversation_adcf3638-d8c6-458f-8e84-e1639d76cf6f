import { Component, EventEmitter, OnDestroy, Output } from '@angular/core';
import { SpeechService } from '../../services/speech.service'; // Import the HTTP service
import { Subscription } from 'rxjs';

interface MessageLogEntry {
  question: string;
  // You might add an 'answer' property here if your backend sends back AI responses
}


@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})

export class HomeComponent implements OnDestroy {
  isListening: boolean = false;
  statusMessage: string = 'Prêt à démarrer le streaming vocal.';
  errorMessage: string | null = null;
  messageLog: MessageLogEntry[] = [];
  openSuggested = false; // To store a log of messages/questions
@Output() openProductModal = new EventEmitter<void>();

  private speechSubscription: Subscription | undefined;

  constructor(private speechService: SpeechService) {}

  toggleSpeech(): void {
    if (!this.isListening) {
      // Start streaming
      this.startSpeechStream();
    } else {
      // Stop streaming (this is mainly for UI, as the backend stream might be continuous)
      this.stopSpeechStream();
    }
  }

  private startSpeechStream(): void {
    this.isListening = true;
    this.statusMessage = 'Démarrage du streaming en cours...';
    this.errorMessage = null; // Clear any previous error messages

    this.speechSubscription = this.speechService.startStreaming().subscribe({
      next: (response: string) => {
        this.statusMessage = response; // Expected: "Streaming started..."
        console.log('Backend response (stream start):', response);

        // Add a log entry, assuming 'response' could be treated as a confirmation message
        this.messageLog.push({ question: response });

        // Since this is a simple HTTP GET that *starts* the backend process,
        // we don't receive continuous audio data or transcriptions back here directly.
        // The backend `streamMicrophone()` is presumed to handle the audio internally.
        // If you need real-time transcription back, you'd need a WebSocket for that.
      },
      error: (error: Error) => {
        this.isListening = false; // Stop listening state on error
        this.statusMessage = 'Erreur lors du démarrage du streaming.';
        this.errorMessage = error.message;
        console.error('Erreur lors du démarrage du streaming vocal:', error);
      },
      complete: () => {
        console.log('Requête de démarrage de streaming terminée.');
        // Note: The HTTP request itself completes. The backend microphone streaming
        // is an asynchronous process that continues on the server.
        // To truly "stop" it, you'd need another endpoint on your Spring Boot side.
      }
    });
  }

  private stopSpeechStream(): void {
    this.isListening = false;
    this.statusMessage = 'Streaming vocal arrêté (côté frontend).';
    // If you had a backend endpoint to stop the stream, you'd call it here:
    // this.speechService.stopStreaming().subscribe(...);

    // Unsubscribe from the HTTP request Observable to clean up resources
    if (this.speechSubscription) {
      this.speechSubscription.unsubscribe();
      this.speechSubscription = undefined;
    }
    console.log('Frontend listening state stopped.');
  }

  ngOnDestroy(): void {
    // Ensure subscription is unsubscribed when the component is destroyed
    if (this.speechSubscription) {
      this.speechSubscription.unsubscribe();
    }
  }
  
}

