// toast-notification.service.ts
import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject } from 'rxjs';

export interface ToastNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  icon?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ToastNotificationService {
  private toastsSubject = new BehaviorSubject<ToastNotification[]>([]);
  public toasts$ = this.toastsSubject.asObservable();
    constructor(private toastr: ToastrService) {}


  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  


  showWarning(title: string, message: string, duration: number = 4000) {
    this.addToast({
      type: 'warning',
      title,
      message,
      duration,
      icon: '⚠️'
    });
  }



  private addToast(toast: Omit<ToastNotification, 'id'>) {
    const newToast: ToastNotification = {
      ...toast,
      id: this.generateId()
    };

    const currentToasts = this.toastsSubject.value;
    this.toastsSubject.next([...currentToasts, newToast]);

    // Auto-remove toast after duration
    if (toast.duration && toast.duration > 0) {
      setTimeout(() => {
        this.removeToast(newToast.id);
      }, toast.duration);
    }
  }

  removeToast(id: string) {
    const currentToasts = this.toastsSubject.value;
    const updatedToasts = currentToasts.filter(toast => toast.id !== id);
    this.toastsSubject.next(updatedToasts);
  }

  clearAll() {
    this.toastsSubject.next([]);
  }
    showSuccess(title: string, message: string) {
    this.toastr.success(message, title);
  }

  showError(title: string, message: string) {
    this.toastr.error(message, title);
  }

  showInfo(title: string, message: string) {
    this.toastr.info(message, title);
  }
}