<div class="max-w-md mx-auto p-0">
  <h3 class="text-xl font-semibold mb-4 text-center text-white">Assistant vocal</h3>

  <!-- Pixel Streaming affiché ici -->
  <div class="relative w-full h-[360px] overflow-hidden rounded-lg mb-4">
    <iframe
      src="http://localhost"
      class="absolute top-[-300px] left-[-650px] w-[1920px] h-[1080px] border-0"
      allow="microphone; autoplay">
    </iframe>
    
    <!-- Indicateur d'état superposé -->
    <div class="absolute top-2 right-2 flex flex-col gap-2">
      <!-- État de connexion -->
      <div class="flex items-center gap-2 bg-black bg-opacity-60 px-3 py-1 rounded-full text-sm">
        <div [class]="isConnected ? 'w-2 h-2 bg-green-400 rounded-full' : 'w-2 h-2 bg-red-400 rounded-full'"></div>
        <span class="text-white">{{ isConnected ? 'Connecté' : 'Déconnecté' }}</span>
      </div>
      
      <!-- <PERSON><PERSON> de lecture -->
      <div *ngIf="isPlaying" class="flex items-center gap-2 bg-indigo-600 bg-opacity-80 px-3 py-1 rounded-full text-sm">
        <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
        <span class="text-white">En lecture...</span>
      </div>
      
      <!-- Mode automatique -->
      <div *ngIf="autoPlayEnabled" class="flex items-center gap-2 bg-green-600 bg-opacity-80 px-3 py-1 rounded-full text-sm">
        <span class="text-white">🤖 Auto</span>
      </div>
    </div>
  </div>

  <!-- Contrôles -->
  <div class="flex flex-col items-center space-y-3">
    
    <!-- Boutons principaux -->
    <div class="flex gap-3">
      <!-- Bouton lecture manuelle (affiché seulement si pas en mode auto ou pas connecté) -->
      <button
        *ngIf="!autoPlayEnabled || !isConnected"
        (click)="playAudio()"
        [disabled]="isPlaying"
        [class]="isPlaying ? 'bg-gray-400 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700'"
        class="text-white px-5 py-2 rounded-lg transition shadow">
        {{ isConnected ? '▶️ Lire audio' : '🚀 Démarrer' }}
      </button>
      
      <!-- Bouton redémarrage -->
      <button
        (click)="restartSession()"
        [disabled]="isPlaying"
        class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition shadow disabled:bg-gray-400 disabled:cursor-not-allowed">
        🔄 Redémarrer
      </button>
      
      <!-- Bouton arrêt -->
      <button
        *ngIf="isConnected"
        (click)="stopSession()"
        class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition shadow">
        🛑 Arrêter
      </button>
    </div>

    <!-- Toggle lecture automatique -->
    <div class="flex items-center gap-3">
      <label class="flex items-center gap-2 cursor-pointer">
        <input
          type="checkbox"
          [checked]="autoPlayEnabled"
          (change)="toggleAutoPlay()"
          class="w-4 h-4 text-indigo-600 rounded focus:ring-indigo-500">
        <span class="text-gray-300 text-sm">Lecture automatique</span>
      </label>
    </div>

    <!-- Informations d'état -->
    <div class="text-center">
      <p *ngIf="autoPlayEnabled && isConnected" class="text-green-400 text-sm">
        🤖 Mode automatique activé - La lecture démarre dès réception
      </p>
      <p *ngIf="!autoPlayEnabled && isConnected" class="text-orange-400 text-sm">
        🎯 Mode manuel - Cliquez sur "Lire audio" pour démarrer
      </p>
      <p *ngIf="!isConnected" class="text-gray-400 text-sm">
        🔌 Service déconnecté - Cliquez sur "Démarrer" pour vous connecter
      </p>
      <p *ngIf="isPlaying" class="text-indigo-400 text-sm mt-1">
        🎵 Audio en cours de lecture...
      </p>
    </div>

    <!-- Instructions -->
    <div class="bg-gray-800 bg-opacity-50 p-3 rounded-lg">
      <p class="text-gray-300 text-xs text-center leading-relaxed">
        <strong>Mode automatique :</strong> L'audio se lance dès qu'il est prêt<br>
        <strong>Mode manuel :</strong> Cliquez pour lancer la lecture<br>
        🎤 Le microphone s'arrête automatiquement pendant la lecture
      </p>
    </div>
  </div>
</div>