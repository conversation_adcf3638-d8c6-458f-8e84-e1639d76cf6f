import { StorageService } from './../services/StorageService';
import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { loadStripe } from '@stripe/stripe-js';
import { PaymentService } from '../services/payment.service';
import { CartService } from '../services/cart.service';
import { AuthService } from '../services/auth.service';
import { ToastNotificationService } from '../services/toast-notification.service';

@Component({
  selector: 'app-paiement',
  templateUrl: './paiement.component.html',
  styleUrls: ['./paiement.component.scss']
})
export class PaiementComponent {
  amount: number;
  userId: string;


  stripePromise = loadStripe('pk_test_51RR9QpRf0GocnO7SGynMXyWfdYWKqqzQFXywGDVJ34BKkWNCnCmY5Y2mbPWU5IdeeSGqKDgBGxiizvKTtlf3dxcl00HSK4VIRI'); // Replace with your actual Stripe public key

  constructor(private router: Router, private paymentService: PaymentService, private cartService: CartService, private authService: AuthService, private StorageService: StorageService,    private toastService: ToastNotificationService
  ) {
    this.amount = this.cartService.getTotalAmount() * 100;
    this.userId = this.StorageService.getUid() ?? '';
    const token = localStorage.getItem('jwt');
    if (!token) {
   this.toastService.showError(
          'Session expirée 🔒',
          'Veuillez vous reconnecter pour accéder à vos commandes'
        );      this.router.navigate(['/']);
    }
  }

  openPaypal() {
     this.toastService.showSuccess(
          ' ',
          'Redirection vers PayPal..✅'
        );
    this.router.navigate(['/confirmation']);
  }

async openStripe() {
  const stripe = await this.stripePromise;

  this.paymentService.createCheckout(this.amount, this.userId)
    .subscribe(async session => {
      if (stripe && session.id) {
        const result = await stripe.redirectToCheckout({ sessionId: session.id });
        if (result.error) {
          alert(result.error.message);
        }
      } else {
          this.toastService.showError(
          'Erreur de chargement ❌',
          'session.id manquant.'
        );
      }
    }, error => {
      console.error('Erreur Stripe:', error);
            this.toastService.showError(
          'Erreur Stripe❌',
          'Échec de la redirection vers Stripe.'
        );
    });
}}
