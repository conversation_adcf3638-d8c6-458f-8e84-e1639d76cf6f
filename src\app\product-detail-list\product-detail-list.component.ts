import { Component, OnChanges, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ProduitService } from '../services/produit.service';
import { Product } from '../model/produit.model';
import { CartService } from '../services/cart.service';
import { CartItem } from '../model/cart-item.model';
import { FavoriteService } from '../services/favorite.service';
import { FavoriteItem } from '../model/favorite-item.model';
import { ToastNotificationService } from '../services/toast-notification.service';

@Component({
  selector: 'app-product-detail-list',
  templateUrl: './product-detail-list.component.html',
  styleUrls: ['./product-detail-list.component.scss']
})
export class ProductDetailListComponent implements OnInit {
 produits: Product[] = [];
product: any;
 currentImageIndex: number = 0;
  currentImage: string = '';
  constructor(
    private route: ActivatedRoute,
    private produitService: ProduitService,
    private cartService: CartService,
    private favoriteService: FavoriteService,
          private toastService: ToastNotificationService
    

  ) {}

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.produitService.getProduitById(id).subscribe(
        (produit: Product) => {
          this.product = produit;
              this.initializeimageUrl();

        },
        (error) => {
          console.error('Erreur lors du chargement du produit :', error);
        }
      );
    }
    
  }

  addToCart(produit: Product) {
    const item: CartItem = {
      productId: produit.idp!,
      nom: produit.nom,
      prix: produit.prix,
      quantity: 1,
      images: produit.imageUrl
    };
    this.cartService.addToCart(item);
  this.toastService.showSuccess(
        'Ajouté au panier ! 🛒',
        `${produit.nom} a été ajouté à votre panier avec succès`
      );  }
  addToFavorites(produit: Product) {
   const prix = isNaN(Number(produit.prixPromotion)) || !produit.prixPromotion
  ? Number(produit.prix)
  : Number(produit.prixPromotion);

  const item: FavoriteItem = {
    productId: produit.idp!,
    nom: produit.nom,
        quantity: 1, // quantité initiale

    prix: prix,
            images: produit.imageUrl,


  };
  this.favoriteService.addToFavorites(item);
this.toastService.showSuccess(
  `${produit.nom}`,
  'a été ajouté aux favoris ✅'
);
  }
initializeimageUrl() {
    if (this.product) {
      // Si le produit a un tableau d'imageUrl
      if (this.product.imageUrl && this.product.imageUrl.length > 0) {
        this.currentImage = this.product.imageUrl[0];
        this.currentImageIndex = 0;
      } 
      // Sinon utiliser l'image principale (rétrocompatibilité)
      else if (this.product.imageUrl) {
        this.currentImage = this.product.imageUrl;
        this.currentImageIndex = 0;
        // Créer un tableau avec une seule image pour la cohérence
        this.product.imageUrl = [this.product.imageUrl];
      }
    }
  }

  // Naviguer vers l'image suivante
  nextImage() {
    if (this.product.imageUrl && this.product.imageUrl.length > 1) {
      this.currentImageIndex = (this.currentImageIndex + 1) % this.product.imageUrl.length;
      this.currentImage = this.product.imageUrl[this.currentImageIndex];
    }
  }

  // Naviguer vers l'image précédente
  previousImage() {
    if (this.product.imageUrl && this.product.imageUrl.length > 1) {
      this.currentImageIndex = this.currentImageIndex === 0 
        ? this.product.imageUrl.length - 1 
        : this.currentImageIndex - 1;
      this.currentImage = this.product.imageUrl[this.currentImageIndex];
    }
  }

  // Définir l'image courante par index
  setCurrentImage(index: number) {
    if (this.product.imageUrl && index >= 0 && index < this.product.imageUrl.length) {
      this.currentImageIndex = index;
      this.currentImage = this.product.imageUrl[index];
    }
  }

  // Navigation des miniatures (pour le scroll horizontal)
  scrollThumbnails(direction: number) {
    if (direction === 1) {
      this.nextImage();
    } else {
      this.previousImage();
    }
  }
    openFullscreen() {
    // Vous pouvez implémenter une modal de galerie ou ouvrir dans une nouvelle fenêtre
    console.log('Ouverture en plein écran de l\'image:', this.currentImage);
    
    // Exemple simple : ouvrir dans une nouvelle fenêtre
    const newWindow = window.open('', '_blank');
    if (newWindow) {
      newWindow.document.write(`
        <html>
          <head>
            <title>${this.product.nom} - Image ${this.currentImageIndex + 1}</title>
            <style>
              body { 
                margin: 0; 
                background: #000; 
                display: flex; 
                justify-content: center; 
                align-items: center; 
                min-height: 100vh; 
              }
              img { 
                max-width: 100%; 
                max-height: 100vh; 
                object-fit: contain; 
              }
            </style>
          </head>
          <body>
            <img src="${this.currentImage}" alt="${this.product.nom}">
          </body>
        </html>
      `);
    }
  }
    private showNotification(message: string, type: 'success' | 'error' | 'info') {
    // Vous pouvez utiliser une library comme ngx-toastr ou créer votre propre système
    alert(message); // Remplacez par votre système de notification
  }
    getDiscountPercentage(): number {
    if (this.product.prixPromotion && this.product.prix) {
      const discount = ((this.product.prix - this.product.prixPromotion) / this.product.prix) * 100;
      return Math.round(discount);
    }
    return 0;
  }
parseAvis(avisStr: string) {
  // Enlever les guillemets en début et fin, ainsi que la virgule finale
  let cleanStr = avisStr.trim();

  // Enlever guillemets " au début et à la fin
  if (cleanStr.startsWith('"')) {
    cleanStr = cleanStr.substring(1);
  }
  if (cleanStr.endsWith('"')) {
    cleanStr = cleanStr.substring(0, cleanStr.length - 1);
  }

  // Enlever la virgule finale s'il y en a une
  if (cleanStr.endsWith(',')) {
    cleanStr = cleanStr.substring(0, cleanStr.length - 1);
  }

  // Maintenant on split sur les virgules pour récupérer chaque champ
  const parts = cleanStr.split(',').map(part => part.trim());

  const avisObj: any = {};

  parts.forEach(part => {
    const separatorIndex = part.indexOf(':');
    if (separatorIndex !== -1) {
      const key = part.substring(0, separatorIndex).trim().toLowerCase();
      const value = part.substring(separatorIndex + 1).trim();
      avisObj[key] = value;
    }
  });

  console.log('Parsed avisObj:', avisObj);
  return avisObj;
}
ajouterAvisAuProduit(productId: string, commentaire: string) {
  this.produitService.addAvis(productId, commentaire).subscribe({
    next: (res) => {
      console.log(res);
      // Actualiser la liste des avis par exemple
    },
    error: (err) => {
      console.error("Erreur lors de l'ajout de l'avis", err);
    }
  });
}


}
