@keyframes fade-in {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

/* Scrollbar personnalisée */
.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
/* CSS personnalisé pour la page produit */

/* Masquer la scrollbar pour les miniatures */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Animation pour le changement d'image */
.image-transition {
  transition: opacity 0.3s ease-in-out;
}

/* Effet hover sur les miniatures */
.thumbnail-hover:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Animation pour les boutons de navigation */
.nav-button {
  backdrop-filter: blur(4px);
  transition: all 0.2s ease;
}

.nav-button:hover {
  transform: scale(1.1);
  background-color: rgba(0, 0, 0, 0.8);
}

/* Effet de pulsation pour l'indicateur actif */
.active-indicator {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Style pour le badge du nombre d'images */
.image-badge {
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Amélioration des transitions générales */
.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Style pour les cartes d'information */
.info-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Animation d'entrée pour les éléments */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Style pour les boutons d'action */
.action-button {
  position: relative;
  overflow: hidden;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.action-button:hover::before {
  left: 100%;
}

/* Responsive pour les petits écrans */
@media (max-width: 768px) {
  .thumbnail-container {
    justify-content: center;
  }
  
  .thumbnail-item {
    width: 60px;
    height: 60px;
  }
  
  .main-image {
    height: 250px;
  }
  
  .nav-button {
    padding: 8px;
  }
  
  .nav-button i {
    font-size: 14px;
  }
}

/* Style pour le mode sombre (optionnel) */
@media (prefers-color-scheme: dark) {
  .bg-white {
    background-color: #1f2937;
    color: white;
  }
  
  .text-gray-700 {
    color: #d1d5db;
  }
  
  .border-gray-200 {
    border-color: #374151;
  }
  
  .bg-gray-50 {
    background-color: #374151;
  }
}

/* Animation de chargement pour les images */
.image-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Effet de zoom sur l'image principale */
.image-zoom {
  cursor: zoom-in;
}

.image-zoom:hover {
  transform: scale(1.02);
}