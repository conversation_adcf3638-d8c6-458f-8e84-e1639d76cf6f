import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UserService } from '../services/user.service'; // Assurez-vous d'importer le service
import { User } from '../model/user.model'; // Créez un modèle pour User si ce n'est pas encore fait
import { StorageService } from '../services/StorageService'; // Service de stockage personnalisé
import { ToastNotificationService } from '../services/toast-notification.service';

@Component({
  selector: 'app-users',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.scss']
})
export class UsersComponent implements OnInit {
  
  users: User[] = [];  // Liste des utilisateurs récupérés depuis le backend
  searchTerm: string = '';
  errorMessage: string = '';
  selectedUser: any = null;
  currentUserRole: string = '';

  constructor(
    private router: Router,
    private userService: UserService,
    private storageService: StorageService ,
                    private toastService: ToastNotificationService
   
  ) {}

  ngOnInit(): void {
    if (this.hasRole('ADMIN')) {
      this.loadUsers();
    }
  }
  
  
  
  loadUsers(): void {
    this.userService.getUsers().subscribe({
      next: (data) => {
        console.log('🔍 Utilisateurs reçus:', data);
        this.users = data;
      },
      error: (err) => {
        console.error('Erreur lors du chargement des utilisateurs', err);
        this.errorMessage = 'Impossible de récupérer les utilisateurs';
      }
    });
  }
  

  // Récupère la liste filtrée d'utilisateurs en fonction du terme de recherche
  get filteredUsers() {
    return this.users.filter(user =>
      // Vérifie si `user.name` et `user.email` sont définis avant de les utiliser
      (user.displayName?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      user.email?.toLowerCase().includes(this.searchTerm.toLowerCase())) &&
      !user.archived
    );
  }

  onArchiveUser(uid: string): void {
    this.userService.archiveUser(uid).subscribe(
      (response: string) => {
        this.toastService.showSuccess(
        'Utilisateur  ! ',
        ` a été archivé avec succès✅ `
      );
        console.log('Utilisateur archivé:', response);
        // Si la réponse contient un message, vous pouvez l'afficher ou prendre d'autres actions
           this.loadUsers();
      },
      (error) => {
        console.error('Erreur lors de l\'archivage de l\'utilisateur', error);
      }
    );
  }
  
  // Action de consultation d'un utilisateur
  viewUser(user: any) {
    this.userService.getUserDetails(user.uid).subscribe(
      user => {
        console.log('Détails de l\'utilisateur :', user);
        this.selectedUser = user;
        console.log('selectedUser :', this.selectedUser);
      },
      error => {
        console.error('Erreur lors de la récupération des détails de l\'utilisateur', error);
      }
    );
  }
  
  
  
  goToDashboard() {
    this.router.navigate(['/dashboard']);
  }
  
  closeModal(): void {
    this.selectedUser = null;
  }

  // Vérifie si l'utilisateur a un rôle spécifique
  hasRole(role: string): boolean {
    const userRole = this.storageService.getItem('role'); // Utilisation du service StorageService
    return userRole === role;
  }
}
