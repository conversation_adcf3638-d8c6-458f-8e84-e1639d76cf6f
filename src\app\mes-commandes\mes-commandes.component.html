<div class="orders-container">
  <div class="breadcrumb">
    <span (click)="goToAccueil()" class="back-link">← Accueil</span>
    <span> / Mes Commandes</span>
  </div>

  <h1>Mes Commandes</h1>

  <div class="sections-container">
    <div *ngFor="let order of orders" class="section-card">
      <table>
        <thead>
          <tr>
            <th>Référence</th>
            <th>Date</th>
            <th>Statut</th>
            <th>Total</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
         <tr [ngClass]="getStatusClass(order.status)">
  <td>{{ order.id }}</td>
  <td>{{ order.orderDate | date: 'dd MMM yyyy, HH:mm' }}</td>
  <td>{{ order.status }}</td>
  <td>{{ order.totalPrice | number:'1.2-2' }} TND</td>
  <td>
    <button (click)="viewOrder(order)">Consulter</button>

    <button
      class="cancel-btn"
      (click)="cancelOrder(order.id ?? '')"
      [disabled]="order.status === OrderStatus.ANNULEE">
      Annuler
    </button>
  </td>
</tr>

        </tbody>
      </table>
    </div>
  </div>

</div>
