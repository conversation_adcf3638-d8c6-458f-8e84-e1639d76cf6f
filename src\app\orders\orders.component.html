<!-- orders.component.html -->
<div *ngIf="isAdmin; else accessDenied" class="orders-container">
  <div class="breadcrumb">
    <span (click)="goToDashboard()" class="back-link">← Dashboard</span>
    <span> / Liste des commandes</span>
  </div>
  <h1>Liste des Commandes</h1>
  
  <input type="text" [(ngModel)]="searchTerm" placeholder="Rechercher par nom client..." />
  
  <div class="sections-container">
    <!-- Boucle sur chaque statut -->
    <div *ngFor="let status of allStatuses" class="status-section">
      <div *ngIf="getOrdersByStatus(status).length > 0" class="section-card">
        <h2 class="status-title" [ngClass]="getStatusClass(status)">
          {{ getStatusLabel(status) }} ({{ getOrdersByStatus(status).length }})
        </h2>
        
        <table>
          <thead>
            <tr>
              <th>R<PERSON><PERSON><PERSON><PERSON>ce</th>
              <th>Client</th>
              <th>Date</th>
              <th>Total</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let order of getOrdersByStatus(status)" [ngClass]="getStatusClass(order.status)">
              <td>{{ order.id }}</td>
              <td>{{ getUserName(order.userId) }}</td>
              <td>{{ order.orderDate | date: 'mediumDate' }}</td>
              <td>{{ order.totalPrice }} €</td>
              <td>
                <select [ngModel]="order.status" (ngModelChange)="onStatusChange(order.id!, $event)">
                  <option *ngFor="let statusOption of allStatuses" [value]="statusOption">
                    {{ getStatusLabel(statusOption) }}
                  </option>
                </select>
              </td>
              <td>
                <button class="view-btn" (click)="viewOrder(order)">📋 Consulter</button>
                <button class="archive-btn" (click)="archiveOrder(order)">📁 Archiver</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  
  <p *ngIf="filteredOrders.length === 0">Aucune commande trouvée.</p>
</div>

<ng-template #accessDenied>
  <div class="orders-container">
    <h1>Accès refusé</h1>
    <p>Cette page est réservée aux administrateurs.</p>
  </div>
</ng-template>