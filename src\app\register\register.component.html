<div class="container">
  <div class="split-layout">
    <div class="image-section">
      <img src="assets/AI.jpg" alt="Background Image" class="background-image">
    </div>

    <div class="form-section">
      <div class="form-container">
        <form [formGroup]="fullRegisterForm" (ngSubmit)="onSubmit()">
          <h2>Inscription </h2>

          <div class="form-group">
            <label for="firstName">Prénom</label>
            <input type="text" id="firstName" formControlName="firstName" placeholder="Entrez votre prénom">
          </div>

          <div class="form-group">
            <label for="lastName">Nom</label>
            <input type="text" id="lastName" formControlName="lastName" placeholder="Entrez votre nom">
          </div>

          <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" formControlName="email" placeholder="Entrez votre email">
          </div>
<!-- Champ Mot de passe -->
<div class="form-group">
  <label for="password">Mot de passe</label>
  <input type="password" id="password" formControlName="password" placeholder="Entrez un mot de passe">
  
  <!-- Erreurs du champ mot de passe -->
  <div *ngIf="fullRegisterForm.get('password')?.touched && fullRegisterForm.get('password')?.invalid" class="error-message">
    <div *ngIf="fullRegisterForm.get('password')?.errors?.['required']">
      Le mot de passe est requis.
    </div>
    <div *ngIf="fullRegisterForm.get('password')?.errors?.['minlength']">
      Le mot de passe doit contenir au moins 6 caractères.
    </div>
  </div>
</div>

<!-- Champ Confirmation du mot de passe -->
<div class="form-group">
  <label for="confirmPassword">Confirmer le mot de passe</label>
  <input type="password" id="confirmPassword" formControlName="confirmPassword" placeholder="Confirmez votre mot de passe">
  
  <!-- Erreurs du champ confirmPassword -->
  <div *ngIf="fullRegisterForm.get('confirmPassword')?.touched && fullRegisterForm.get('confirmPassword')?.invalid" class="error-message">
    <div *ngIf="fullRegisterForm.get('confirmPassword')?.errors?.['required']">
      La confirmation du mot de passe est requise.
    </div>
  </div>

  <!-- Erreur si les deux mots de passe ne correspondent pas -->
  <div *ngIf="fullRegisterForm.errors?.['mismatch'] && fullRegisterForm.get('confirmPassword')?.touched" class="error-message">
    Les mots de passe ne correspondent pas.
  </div>
</div>


          <div class="form-group">
            <label for="address">Adresse</label>
            <input type="text" id="address" formControlName="address" placeholder="Entrez votre adresse">
          </div>

          <div class="form-group">
            <label for="phoneNumber">Téléphone</label>
            <input type="text" id="phoneNumber" formControlName="phoneNumber" placeholder="Entrez votre numéro de téléphone">
          </div>

          <button type="submit" [disabled]="fullRegisterForm.invalid">S'inscrire</button>
        </form>
      </div>
    </div>
  </div>
</div>
