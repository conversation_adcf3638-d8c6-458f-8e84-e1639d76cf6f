nav {
    background-color: #1f2937; /* bg-gray-800 */
    color: white; /* text-white */
    padding: 1rem; /* p-4 */
    display: flex; /* flex */
    justify-content: center; /* justify-center */
    align-items: center; /* items-center */
    position: relative; /* relative */
  }
  
  .fade-text {
    font-size: 1.125rem; /* text-lg */
    font-weight: bold; /* font-bold */
    text-align: center; /* text-center */
    position: absolute; /* absolute */
    left: 50%; /* left-1/2 */
    transform: translateX(-50%); /* transform -translate-x-1/2 */
  }
  
  .right-container {
    position: absolute; /* absolute */
    right: 1rem; /* right-4 */
    display: flex; /* flex */
    align-items: center; /* items-center */
    gap: 1rem; /* gap-4 */
  }
  
  .cart-icon {
    width: 2rem; /* w-8 */
    height: 2rem; /* h-8 */
    cursor: pointer; /* cursor-pointer */
    transition: opacity 0.3s ease; /* transition-opacity */
  }
  
  .cart-icon:hover {
    opacity: 0.8; /* hover:opacity-80 */
  }
  
  .login-button {
    background-color: #3b82f6; /* bg-blue-500 */
    padding: 0.5rem 1rem; /* px-4 py-2 */
    border-radius: 0.25rem; /* rounded */
    transition: background-color 0.3s ease; /* transition-colors */
  }
  
  .login-button:hover {
    background-color: #2563eb; /* hover:bg-blue-600 */
  }
  .cart-modal {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .cart-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    width: 600px;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  }
  
  .cart-title {
    font-size: 24px;
    margin-bottom: 20px;
    text-align: center;
  }
  
  .cart-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
  
    th, td {
      padding: 12px;
      text-align: center;
      border-bottom: 1px solid #ddd;
    }
  
    th {
      background: #007bff; // Fond bleu
      color: white; // Texte blanc
      font-weight: bold;
    }
  
  
    .quantity-input {
      width: 60px;
      padding: 5px;
      text-align: center;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
  
    .delete-button {
      background-color: #ff4d4d;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s;
  
      &:hover {
        background-color: #ff1a1a;
      }
    }
  }
  
  .cart-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
  
    .total-amount {
      font-size: 18px;
      font-weight: bold;
    }
  
    .buttons-container {
      display: flex;
      gap: 10px;
  
      .checkout-button {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;
  
        &:hover {
          background-color: #0056b3;
        }
      }
  
      .close-button {
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;
  
        &:hover {
          background-color: #5a6268;
        }
      }
    }
  }
  h2 {
    margin-bottom: 15px;
    font-size: 2rem;
    font-weight: bold;
    color: transparent;
    background: linear-gradient(90deg, #007bff, #4aa5d2);
    -webkit-background-clip: text;
    background-clip: text;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  }
  .cart-modal {
    background-color: rgba(0, 0, 0, 0.5) !important;
  }
  .favorites-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 1rem;
}

.favorite-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  background: #f9f9f9;
}

.favorite-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.favorite-details {
  flex: 1;
}

.favorite-name {
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #333;
}

.favorite-price {
  color: #28a745;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.favorite-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.add-to-cart-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.3s;
}

.add-to-cart-button:hover {
  background: #0056b3;
}

.remove-favorite-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.3s;
}

.remove-favorite-button:hover {
  background: #c82333;
}
