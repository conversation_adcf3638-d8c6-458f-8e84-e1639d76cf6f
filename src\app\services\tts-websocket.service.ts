// src/app/services/tts-websocket.service.ts
import { EventEmitter, Injectable } from '@angular/core';
import { SpeechService } from './speech.service';

// Interface pour les données de produits
interface ProductData {
  chunk_index: number;
  total_chunks: number;
  products: any[];
  is_last_chunk: boolean;
  message?: string;
}

interface SessionData {
  session_id: string;
  text_length: number;
  products_count: number;
  timestamp: string;
}

@Injectable({
  providedIn: 'root'
})
export class TtsWebSocketService {
  private socket: WebSocket | null = null;

  // File d'attente des audio blobs à jouer
  private audioQueue: Blob[] = [];
  private isPlaying = false;
  private isConnected = false; // Nouveau: état de connexion
  private autoPlayEnabled = false; // Nouveau: contrôle du play automatique
  
  public onSpeaking = new EventEmitter<boolean>();
  public onAudioReady = new EventEmitter<void>(); // Nouveau: émis quand audio est prêt

  // Gestion des produits
  public onProductsReceived = new EventEmitter<any[]>();
  public onSessionStart = new EventEmitter<SessionData>();
  public onSessionEnd = new EventEmitter<any>();

  // Stockage temporaire des chunks de produits
  private productChunks: any[][] = [];
  private expectedChunks = 0;
  private receivedChunks = 0;

  constructor(private speechService: SpeechService) {}

  /**
   * Connecte et active la lecture automatique
   */
  connectWithAutoPlay(): void {
    this.autoPlayEnabled = true;
    this.connect();
  }

  /**
   * Connecte sans lecture automatique (mode manuel)
   */
  connect(): void {
    const sessionId = localStorage.getItem('uid') || '';
    const url = `ws://127.0.0.1:8092/ws/tts/${sessionId}`;

    this.socket = new WebSocket(url);

    this.socket.onopen = () => {
      console.log('✅ WebSocket connecté');
      console.log(`📨 Session ID envoyé : ${sessionId}`);
      this.isConnected = true;

      // Réinitialiser les données de produits
      this.resetProductsData();
    };

    this.socket.onmessage = (event) => {
      const data = event.data;

      // Gestion des blobs audio
      if (data instanceof Blob) {
        this.audioQueue.push(data);
        console.log(`🎵 Audio blob reçu (${this.audioQueue.length} dans la queue)`);
        
        // Lecture automatique si activée
        if (this.autoPlayEnabled && !this.isPlaying) {
          console.log('🚀 Démarrage automatique de la lecture audio');
          this.playAudioSequentially();
        }
        
        // Émettre l'événement audio ready
        this.onAudioReady.emit();
        return;
      }

      // Gestion des messages JSON
      try {
        const json = JSON.parse(data);
        this.handleJsonMessage(json);
      } catch {
        console.log('📥 Données reçues (non JSON et non Blob)');
      }
    };

    this.socket.onerror = (error) => {
      console.error('💥 Erreur WebSocket :', error);
      this.isConnected = false;
    };

    this.socket.onclose = () => {
      console.log('🔌 WebSocket fermé');
      this.isConnected = false;
    };
  }

  private handleJsonMessage(json: any): void {
    switch (json.type) {
      case 'session_start':
        console.log('🚀 Session démarrée:', json);
        this.expectedChunks = Math.ceil(json.products_count / 50); // Assumant chunk_size = 50
        this.onSessionStart.emit(json);

        // Stocker les métadonnées de session
        this.storeSessionMetadata(json);
        break;

      case 'products_data':
        console.log(`📦 Chunk produits ${json.chunk_index + 1}/${json.total_chunks}:`, json.products.length, 'produits');
        this.handleProductsChunk(json);
        break;

      case 'audio_start':
        console.log('🔊 Début du streaming audio');
        break;

      case 'audio_end':
        console.log('✅ Fin du streaming audio');
        break;

      case 'session_end':
        console.log('🏁 Session terminée:', json);
        this.onSessionEnd.emit(json);
        break;

      case 'error':
        console.error('❌ Erreur du serveur :', json.message);
        break;

      default:
        console.log('📨 Message JSON reçu:', json);
    }
  }

  private handleProductsChunk(data: ProductData): void {
    // Stocker le chunk
    this.productChunks[data.chunk_index] = data.products;
    this.receivedChunks++;

    console.log(`📥 Chunk ${data.chunk_index + 1}/${data.total_chunks} reçu (${data.products.length} produits)`);

    // Si c'est le dernier chunk ou si on a tous les chunks
    if (data.is_last_chunk || this.receivedChunks >= data.total_chunks) {
      this.consolidateAndStoreProducts();
    }
  }

  private consolidateAndStoreProducts(): void {
    // Consolider tous les chunks en une seule liste
    const allProducts: any[] = [];

    for (let i = 0; i < this.productChunks.length; i++) {
      if (this.productChunks[i]) {
        allProducts.push(...this.productChunks[i]);
      }
    }

    console.log(`✅ Tous les produits consolidés: ${allProducts.length} produits`);

    // Stocker dans localStorage
    this.storeProductsInLocalStorage(allProducts);

    // Émettre l'événement avec tous les produits
    this.onProductsReceived.emit(allProducts);

    // Réinitialiser pour la prochaine session
    this.resetProductsData();
  }

  private storeProductsInLocalStorage(products: any[]): void {
    try {
      const sessionId = localStorage.getItem('uid') || '';
      const timestamp = new Date().toISOString();

      const productData = {
        session_id: sessionId,
        timestamp: timestamp,
        products_count: products.length,
        products: products,
        created_at: timestamp
      };

      // Stocker les produits de la session actuelle
      localStorage.setItem('current_session_products', JSON.stringify(productData));

      // Stocker dans l'historique des sessions
      this.addToProductsHistory(productData);

      console.log(`💾 ${products.length} produits sauvegardés dans localStorage`);
      console.log('🔑 Clés localStorage:', Object.keys(localStorage));

    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde des produits:', error);
    }
  }

  private addToProductsHistory(productData: any): void {
    try {
      const historyKey = 'products_history';
      const existingHistory = JSON.parse(localStorage.getItem(historyKey) || '[]');

      // Ajouter la nouvelle session
      existingHistory.unshift(productData);

      // Garder seulement les 10 dernières sessions
      const limitedHistory = existingHistory.slice(0, 10);

      localStorage.setItem(historyKey, JSON.stringify(limitedHistory));

      console.log(`📚 Historique mis à jour: ${limitedHistory.length} sessions sauvegardées`);

    } catch (error) {
      console.error('❌ Erreur lors de la mise à jour de l\'historique:', error);
    }
  }

  private storeSessionMetadata(sessionData: SessionData): void {
    try {
      localStorage.setItem('current_session_metadata', JSON.stringify(sessionData));
      console.log('📋 Métadonnées de session sauvegardées');
    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde des métadonnées:', error);
    }
  }

  private resetProductsData(): void {
    this.productChunks = [];
    this.expectedChunks = 0;
    this.receivedChunks = 0;
  }

  disconnect(): void {
    this.socket?.close();
    this.resetProductsData();
    this.isConnected = false;
    this.autoPlayEnabled = false;
  }

  // === NOUVELLES MÉTHODES DE CONTRÔLE ===

  /**
   * Active/désactive la lecture automatique
   */
  setAutoPlay(enabled: boolean): void {
    this.autoPlayEnabled = enabled;
    console.log(`🔄 Lecture automatique ${enabled ? 'activée' : 'désactivée'}`);
  }

  /**
   * Vérifie si la lecture automatique est activée
   */
  isAutoPlayEnabled(): boolean {
    return this.autoPlayEnabled;
  }

  /**
   * Vérifie si le service est connecté
   */
  isServiceConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Lance manuellement la lecture si des audios sont en attente
   */
  playManually(): void {
    if (this.audioQueue.length > 0 && !this.isPlaying) {
      console.log('🎵 Lecture manuelle déclenchée');
      this.playAudioSequentially();
    } else if (this.audioQueue.length === 0) {
      console.log('⚠️ Aucun audio en attente');
    } else {
      console.log('⚠️ Lecture déjà en cours');
    }
  }

  // === MÉTHODES UTILITAIRES POUR RÉCUPÉRER LES DONNÉES ===

  /**
   * Récupère les produits de la session actuelle
   */
  getCurrentSessionProducts(): any[] {
    try {
      const data = localStorage.getItem('current_session_products');
      return data ? JSON.parse(data).products : [];
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des produits actuels:', error);
      return [];
    }
  }

  /**
   * Récupère l'historique des produits de toutes les sessions
   */
  getProductsHistory(): any[] {
    try {
      return JSON.parse(localStorage.getItem('products_history') || '[]');
    } catch (error) {
      console.error('❌ Erreur lors de la récupération de l\'historique:', error);
      return [];
    }
  }

  /**
   * Récupère les métadonnées de la session actuelle
   */
  getCurrentSessionMetadata(): SessionData | null {
    try {
      const data = localStorage.getItem('current_session_metadata');
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des métadonnées:', error);
      return null;
    }
  }

  /**
   * Nettoie les données de localStorage
   */
  clearStoredData(): void {
    localStorage.removeItem('current_session_products');
    localStorage.removeItem('current_session_metadata');
    console.log('🧹 Données de session nettoyées');
  }

  /**
   * Nettoie tout l'historique
   */
  clearProductsHistory(): void {
    localStorage.removeItem('products_history');
    console.log('🧹 Historique des produits nettoyé');
  }

  // === MÉTHODES AUDIO ===

  private async playAudioSequentially(): Promise<void> {
    if (this.isPlaying || this.audioQueue.length === 0) return;

    this.isPlaying = true;
    this.onSpeaking.emit(true);
    console.log('🎵 Début de la séquence audio automatique');

    // 🛑 Stop micro une seule fois au début
    this.speechService.stopStreaming().subscribe({
      next: res => console.log('🎤 Micro arrêté au début de la lecture audio :', res),
      error: err => console.error('Erreur arrêt micro :', err)
    });

    // Boucle récursive interne pour lire tous les audios en série
    const playNext = async () => {
      if (this.audioQueue.length === 0) {
        // ✅ Fin de la file, on relance le micro
        this.isPlaying = false;
        this.onSpeaking.emit(false);
        console.log('✅ Fin de la séquence audio automatique');
        
        this.speechService.startStreaming().subscribe({
          next: res => console.log('🎤 Micro redémarré après tous les audios'),
          error: err => console.error('Erreur redémarrage micro :', err)
        });
        return;
      }

      const blob = this.audioQueue.shift()!;
      const audioUrl = URL.createObjectURL(blob);
      const audio = new Audio(audioUrl);

      audio.onended = async () => {
        URL.revokeObjectURL(audioUrl);
        console.log('▶️ Audio terminé, suivant...');
        await playNext(); // lecture du suivant
      };

      audio.onerror = async (e) => {
        console.error('Erreur de lecture audio :', e);
        await playNext(); // passer au suivant malgré erreur
      };

      try {
        await audio.play();
        console.log('▶️ Audio en cours de lecture...');
      } catch (err) {
        console.error('Erreur play() :', err);
        await playNext(); // passer au suivant si échec
      }
    };

    await playNext(); // commence la chaîne de lecture
  }

  stopMic(): void {
    this.speechService.stopStreaming().subscribe({
      next: res => console.log('🎤 Micro arrêté manuellement avant lecture :', res),
      error: err => console.error('Erreur arrêt micro :', err)
    });
  }
}