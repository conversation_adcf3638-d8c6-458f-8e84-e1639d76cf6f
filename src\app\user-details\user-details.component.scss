/* --- <PERSON>ER DETAILS CONTAINER & CARD STYLE --- */
.user-details-container {
    max-width: 800px;
    margin: auto;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    position: relative;
    min-height: 80vh;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }
  
  /* Card Style */
  .card {
    background: #ffffff;
    border: 1px solid #ddd;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    text-align: center;
  }
  
  /* Hover effect on card */
  .card:hover {
    transform: scale(1.02);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  }
  
  /* --- HEADER & TITLE --- */
  h1 {
    font-size: 2.2rem;
    font-weight: bold;
    color: transparent;
    background: linear-gradient(90deg, #007bff, #4aa5d2);
    -webkit-background-clip: text;
    background-clip: text;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
    margin-bottom: 30px;
  }
  
  /* --- BACK LINK STYLE --- */
  .back-link {
    position: absolute;
    top: 20px;
    left: 20px;
    cursor: pointer;
    color: #007bff;
    font-weight: bold;
    text-decoration: none;
    transition: color 0.3s ease;
  }
  
  .back-link:hover {
    color: #0056b3;
    text-decoration: underline;
  }
  
  /* --- USER DETAILS TEXT STYLE --- */
  .user-details {
    color: black;
    font-size: 1.1rem;
    margin-bottom: 15px;
    text-align: left;
  }
  
  .user-details strong {
    color: #007bff; /* Blue color for labels */
    font-weight: bold;
  }
  
  .user-details .detail {
    color: black; /* Text color black for values */
    font-weight: normal;
  }
  
  .user-details p {
    margin: 10px 0;
  }
  
  /* --- BUTTON STYLE --- */
  button.return-btn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
    text-align: center;
    margin-top: 20px;
  }
  
  button.return-btn:hover {
    background-color: #0056b3;
  }
  
  /* --- RESPONSIVE DESIGN --- */
  @media screen and (max-width: 768px) {
    .user-details-container {
      padding: 15px;
    }
  
    h1 {
      font-size: 1.8rem;
    }
  
    .card {
      padding: 15px;
    }
  
    .user-details {
      font-size: 1rem;
    }
  
    button.return-btn {
      padding: 6px 12px;
    }
  }
  