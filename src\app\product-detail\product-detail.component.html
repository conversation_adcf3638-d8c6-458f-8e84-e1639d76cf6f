<div class="fixed inset-0 bg-black bg-opacity-70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
  <div class="bg-white w-full max-w-4xl max-h-[90vh] rounded-2xl shadow-2xl overflow-hidden relative transform transition-all duration-300 animate-fade-in">
    
    <!-- Header avec gradient -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 relative">
      <button (click)="close.emit()" 
              class="absolute top-4 right-4 w-10 h-10 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-md font-bold text-xl">
        X
      </button>
      
      <h2 class="text-3xl font-bold mb-2 pr-12">{{ product?.nom }}</h2>
      <div class="flex items-center space-x-4">
        <span class="bg-white bg-opacity-20 text-gray-900 dark:text-white px-3 py-1 rounded-full text-sm font-medium backdrop-blur-sm shadow-sm">
          <i class="fas fa-tag mr-1"></i>{{ product.categorie }}
        </span>
        <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm font-medium backdrop-blur-sm"
              [ngClass]="product.stock > 1 ? 'text-green-600' : 'text-red-600'">
          <i class="fas fa-boxes mr-1"></i>
          {{ product.stock > 1 ? 'En stock' : 'Hors stock' }}
        </span>
      </div>
    </div>

    <!-- Contenu principal -->
    <div class="overflow-y-auto max-h-[calc(90vh-120px)]">
      <div class="grid md:grid-cols-2 gap-8 p-6">
        
        <!-- Section Images avec Carrousel -->
        <div class="space-y-4">
          
          <!-- Image principale avec carrousel -->
          <div class="relative group">
            <!-- Image principale -->
            <div class="relative overflow-hidden rounded-xl shadow-lg">
              <img [src]="currentImage" 
                   [alt]="product.nom" 
                   class="w-full h-80 object-cover transition-transform duration-300 hover:scale-105">
              
              <!-- Overlay gradient au hover -->
              <div class="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-0 group-hover:opacity-30 rounded-xl transition-opacity duration-300"></div>
              
              <!-- Boutons navigation -->
              <div *ngIf="product.imageUrl && product.imageUrl.length > 1" class="absolute inset-0 flex items-center justify-between p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <button (click)="previousImage()" 
                        class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white p-2 rounded-full transition-all duration-200">
                  <i class="fas fa-chevron-left"></i>
                </button>
                <button (click)="nextImage()" 
                        class="bg-black bg-opacity-50 hover:bg-opacity-70 text-white p-2 rounded-full transition-all duration-200">
                  <i class="fas fa-chevron-right"></i>
                </button>
              </div>
              
              <!-- Indicateurs de pagination -->
              <div *ngIf="product.imageUrl && product.imageUrl.length > 1" 
                   class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                <button *ngFor="let imageUrl of product.imageUrl; let i = index"
                        (click)="setCurrentImage(i)"
                        class="w-2 h-2 rounded-full transition-all duration-200"
                        [ngClass]="currentImageIndex === i ? 'bg-white' : 'bg-white bg-opacity-50'">
                </button>
              </div>
            </div>
          </div>
          
          <!-- Miniatures des images -->
          <div *ngIf="product.imageUrl && product.imageUrl.length > 1" 
               class="flex space-x-2 overflow-x-auto pb-2">
            <button *ngFor="let imageUrl of product.imageUrl; let i = index"
                    (click)="setCurrentImage(i)"
                    class="flex-shrink-0 relative overflow-hidden rounded-lg border-2 transition-all duration-200"
                    [ngClass]="currentImageIndex === i ? 'border-blue-500 shadow-lg' : 'border-gray-200 hover:border-gray-300'">
              <img [src]="imageUrl" 
                   [alt]="product.nom + ' - Image ' + (i + 1)"
                   class="w-16 h-16 object-cover">
              <!-- Overlay pour l'image active -->
              <div *ngIf="currentImageIndex === i" 
                   class="absolute inset-0 bg-blue-500 bg-opacity-20"></div>
            </button>
          </div>
          
          <!-- Compteur d'images -->
          <div *ngIf="product.imageUrl && product.imageUrl.length > 1" 
               class="text-center text-sm text-gray-600">
            {{ currentImageIndex + 1 }} / {{ product.imageUrl.length }} images
          </div>
          
          <!-- Prix avec design attractif -->
          <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border border-green-200">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-600 mb-1">Prix</p>
                <!-- Prix promotion si disponible -->
                <div *ngIf="product.prixPromotion && product.prixPromotion < product.prix" class="space-y-1">
                  <p class="text-3xl font-bold text-green-600">{{ product.prixPromotion | currency:'EUR':'symbol':'1.2-2':'fr-FR' }}</p>
                  <p class="text-lg line-through text-gray-500">{{ product.prix | currency:'EUR':'symbol':'1.2-2':'fr-FR' }}</p>
                  <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                    PROMO
                  </span>
                </div>
                <!-- Prix normal -->
                <p *ngIf="!(product.prixPromotion && product.prixPromotion < product.prix)" 
                   class="text-3xl font-bold text-green-600">{{ product.prix | currency:'EUR':'symbol':'1.2-2':'fr-FR' }}</p>
              </div>
              <div class="bg-green-500 text-white p-4 rounded-full">
                <i class="fas fa-euro-sign text-xl"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Section Détails -->
        <div class="space-y-6">
          
          <!-- Caractéristiques -->
          <div class="bg-purple-50 p-4 rounded-xl border border-purple-200">
            <h3 class="flex items-center text-lg font-semibold text-purple-800 mb-2">
              <i class="fas fa-list-ul mr-3 text-purple-600"></i>
              Caractéristiques
            </h3>
            <p class="text-purple-700">{{ product.caracteristiques }}</p>
          </div>

          <!-- Grille d'informations -->
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            
            <!-- Couleurs -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h4 class="flex items-center font-semibold text-gray-800 mb-2">
                <i class="fas fa-palette mr-2 text-pink-500"></i>
                Couleurs
              </h4>
              <div class="flex flex-wrap gap-1">
                <span *ngFor="let couleur of product.couleur" 
                      class="bg-pink-100 text-pink-800 px-2 py-1 rounded-full text-xs font-medium">
                  {{ couleur }}
                </span>
              </div>
            </div>

            <!-- Matière -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h4 class="flex items-center font-semibold text-gray-800 mb-2">
                <i class="fas fa-gem mr-2 text-orange-500"></i>
                Matière
              </h4>
              <p class="text-gray-700 text-sm">{{ product.matiere }}</p>
            </div>

            <!-- Occasions -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h4 class="flex items-center font-semibold text-gray-800 mb-2">
                <i class="fas fa-calendar-alt mr-2 text-green-500"></i>
                Occasions
              </h4>
              <div class="flex flex-wrap gap-1">
                <span *ngFor="let occasion of product.occasion" 
                      class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                  {{ occasion }}
                </span>
              </div>
            </div>

            <!-- Styles -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h4 class="flex items-center font-semibold text-gray-800 mb-2">
                <i class="fas fa-tshirt mr-2 text-purple-500"></i>
                Styles
              </h4>
              <div class="flex flex-wrap gap-1">
                <span *ngFor="let style of product.style" 
                      class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium">
                  {{ style }}
                </span>
              </div>
            </div>

            <!-- Tailles -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h4 class="flex items-center font-semibold text-gray-800 mb-2">
                <i class="fas fa-ruler mr-2 text-blue-500"></i>
                Tailles
              </h4>
              <div class="flex flex-wrap gap-1">
                <span *ngFor="let taille of product.taille" 
                      class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                  {{ taille }}
                </span>
              </div>
            </div>

            <!-- Saison -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h4 class="flex items-center font-semibold text-gray-800 mb-2">
                <i class="fas fa-sun mr-2 text-yellow-500"></i>
                Saison
              </h4>
              <p class="text-gray-700 text-sm">{{ product.saison }}</p>
            </div>
          </div>

          <!-- Boutons d'action -->
          <div class="flex gap-3 pt-4">
            <button (click)="addToCart(product)"
                  class="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
            <i class="fas fa-shopping-cart mr-2"></i>
            Ajouter au panier
          </button>
            <button (click)="addToFavorites(product)"
                    class="bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-6 rounded-xl transition-colors duration-200">
              <i class="fas fa-heart mr-2"></i>
              Favoris
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>