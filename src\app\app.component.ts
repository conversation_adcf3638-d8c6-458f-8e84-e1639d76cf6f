import { Component } from '@angular/core';
import { AuthService } from './services/auth.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent {
openLoginModal() {
throw new Error('Method not implemented.');
}
constructor(public authService: AuthService) {
  this.authService.loadToken();
}
  title = 'heracles';
showLoginModal: any;
}
