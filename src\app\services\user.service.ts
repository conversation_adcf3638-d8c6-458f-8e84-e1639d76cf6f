import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { User } from '../model/user.model';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { catchError } from 'rxjs/operators';
import { getAuth, reauthenticateWithCredential, EmailAuthProvider, updatePassword } from 'firebase/auth'; // Import modulaire de Firebase

@Injectable({
  providedIn: 'root'
})
export class UserService {


  private apiUrl = 'http://localhost:8081/api/users';  // URL de l'API backend

  constructor(private http: HttpClient,
    private afAuth: AngularFireAuth,
    private firestore: AngularFirestore
  ) { }

  // Récupère la liste des utilisateurs
  getUsers(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}`);
  }

  // Récupère un utilisateur par son ID
  getUserById(id: string): Observable<any> {
  
    return this.http.get<any>(`${this.apiUrl}/${id}`);
  }
  

  // Archiver un utilisateur (par exemple, changer son statut "actif" en "archivé")
  archiveUser(uid: string): Observable<any> {
    return this.http.patch(`${this.apiUrl}/archive/${uid}`, null);  // PATCH requête pour archiver
  }
  getUserDetails(uid: string) {
    return this.http.get<User>(`${this.apiUrl}/${uid}`);
  }
  updateUser(user: User) {
    return this.http.put<User>(`${this.apiUrl}/users/${user.uid}`, user);
  }
  updatePassword(oldPassword: string, newPassword: string): Observable<any> {
  const auth = getAuth();  // Utilisation de getAuth() pour obtenir l'authentification
  const currentUser = auth.currentUser;  // Récupération de l'utilisateur courant via auth

  if (currentUser) {
    return new Observable(observer => {
      reauthenticateWithCredential(
        currentUser,
        EmailAuthProvider.credential(currentUser.email!, oldPassword)
      ).then(() => {
        // Mise à jour du mot de passe
        updatePassword(currentUser, newPassword)
          .then(() => {
            observer.next('Mot de passe mis à jour avec succès');
            observer.complete();
          })
          .catch(error => {
            observer.error('Erreur lors de la mise à jour du mot de passe: ' + error.message);
          });
      }).catch(error => {
        observer.error('Échec de la réauthentification: ' + error.message);
      });
    }).pipe(
      catchError(error => {
        console.error('Erreur de mise à jour du mot de passe:', error);
        return of(`Erreur: ${error.message}`);
      })
    );
  } else {
    return of('Utilisateur non connecté');
  }
}
updateUserCoordinates(uid: string, phoneNumber: string, address: string): Observable<any> {
  const url = `${this.apiUrl}/update-coordinates/${uid}`;  // Inclure l'UID dans l'URL
  const body = { phoneNumber, address };  // Pas besoin d'inclure l'UID dans le body
  return this.http.put(url, body);  // Envoyer les nouvelles coordonnées
}

}
