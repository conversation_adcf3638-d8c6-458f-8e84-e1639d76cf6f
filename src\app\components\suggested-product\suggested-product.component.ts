import { Component, OnInit, OnDestroy, EventEmitter, Output, Input } from '@angular/core'; // <- ajoute OnDestroy
import { Product } from '../../model/produit.model';
import { ProduitService } from '../../services/produit.service';
import { forkJoin } from 'rxjs';
import { CartService } from '../../services/cart.service';
import { CartItem } from '../../model/cart-item.model';
import { Router } from '@angular/router';
import { ToastNotificationService } from '../../services/toast-notification.service';

@Component({
  selector: 'app-suggested-product',
  templateUrl: './suggested-product.component.html',
  styleUrls: ['./suggested-product.component.scss']
})
export class SuggestedProductComponent implements OnInit, OnDestroy {
goToAllProducts() {
  this.router.navigate(['/produits']); // adapte cette route si besoin
}
historyProducts: Product[] = [];

  allProductNames: string[] = [];
  loading = false;
  error: string | null = null;
  products: Product[] = [];
@Output() openProductModal = new EventEmitter<void>();

  private previousHistory = '';
  private intervalId: any;

  constructor(private produitService: ProduitService,private cartService: CartService,private router: Router, private toastService: ToastNotificationService) {}

  ngOnInit(): void {
    this.loadProductsByNameFromHistory();
    this.startAutoReload(); // démarre le check auto
  }

  ngOnDestroy(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId); // nettoyage à la destruction du composant
    }
  }

  private startAutoReload(): void {
    this.previousHistory = localStorage.getItem('products_history') || '';

    this.intervalId = setInterval(() => {
      const currentHistory = localStorage.getItem('products_history') || '';
      if (currentHistory !== this.previousHistory) {
        this.previousHistory = currentHistory;
        this.loadProductsByNameFromHistory();
      }
    }, 3000); // vérifie toutes les 3 secondes
  }

  private extractAllProductNamesFromHistory(): string[] {
    const historyStr = localStorage.getItem('products_history');
    if (!historyStr) return [];

    try {
      const history = JSON.parse(historyStr);
      const allNames: string[] = [];

      history.forEach((session: any) => {
        if (Array.isArray(session.products)) {
          session.products.forEach((product: any) => {
            if (product.nom) {
              allNames.push(product.nom);
            }
          });
        }
      });

      return allNames;
    } catch (err) {
      console.error('Erreur lors du parsing de products_history:', err);
      return [];
    }
  }

  loadProductsByNameFromHistory(): void {
    this.loading = true;
    this.error = null;

    this.allProductNames = this.extractAllProductNamesFromHistory();
    this.allProductNames = [...new Set(this.allProductNames)];

    if (this.allProductNames.length === 0) {
      this.allProducts = []; // vide la liste
       this.openProductModal.emit();
      this.loading = false;

      return;
    }

    const observables = this.allProductNames.map(name =>
      this.produitService.getProductsByName(name)
    );

    forkJoin(observables).subscribe({
      next: (results: Product[][]) => {
        this.allProducts = results.flat();
        this.historyProducts = this.allProducts;
        this.loading = false;
      
      },
      error: (err) => {
        console.error('Erreur lors de la récupération des produits :', err);
        this.error = 'Erreur lors de la récupération des produits.';
        this.loading = false;
          this.toastService.showError(
          'Erreur de chargement',
          'Impossible de charger les produits suggérés'
        );

      }
    });
  }
  @Input() open = false;
  @Input() allProducts: any[] = [];  // Ou utilise ton interface Produit
  selectedProduct: any | null = null;

  close() {
    this.open = false;
  }

  addToCart(produit: Product) {
    const item: CartItem = {
      productId: produit.idp!,
      nom: produit.nom,
      prix: produit.prix,
      quantity: 1,
      images: produit.imageUrl
    };
    this.cartService.addToCart(item);
       this.toastService.showSuccess(
        'Ajouté au panier ! 🛒',
        `${produit.nom} a été ajouté à votre panier avec succès`
      );
  }

  showDetails(product: any) {
    this.selectedProduct = product;
      this.toastService.showInfo(
      'Détails du produit',
      `Affichage des détails pour ${product.nom}`
    );
  
  }
  isHistoryModalOpen = false;
   openHistoryModal() {
    this.isHistoryModalOpen = true;
  }
  closeHistoryModal() {
    this.isHistoryModalOpen = false;
  }
  loadMore() {
  // Si tu as une pagination, tu peux implémenter ici la logique pour charger plus de produits
  console.log('Chargement de plus de produits...');
  // Par exemple, appeler ton service avec un offset/page supplémentaire
}

}
