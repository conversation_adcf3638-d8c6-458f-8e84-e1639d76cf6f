import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { OrderService } from '../services/order.service';
import { ProduitService } from '../services/produit.service';

@Component({
  selector: 'app-payment-success',
  templateUrl: './payment-success.component.html',
  styleUrls: ['./payment-success.component.scss']
})
export class PaymentSuccessComponent implements OnInit {

  constructor(
    private orderService: OrderService,
    private produitService: ProduitService,
    private router: Router
  ) {}

  ngOnInit(): void {
    const storedCart = localStorage.getItem('cart');
    const userId = localStorage.getItem('uid');

    if (storedCart && userId) {
      const cartItems = JSON.parse(storedCart);

      Promise.all(
        cartItems.map(async (item: any) => {
          const product = await this.produitService.getProduitById(item.productId).toPromise();
          return {
            quantity: item.quantity,
            price: item.prix,
            product: product
          };
        })
      ).then((items) => {
        const totalPrice = items.reduce((sum, item) => sum + item.price * item.quantity, 0);

        const order = {
          userId,
          items,
          totalPrice,
          orderDate: new Date().toISOString()
        };

        this.orderService.createOrderAfterPayment(order).subscribe({
          next: () => {
            console.log('Order successfully saved after payment ✅');
            localStorage.removeItem('order');
            localStorage.removeItem('cart');
          },
          error: err => {
            console.error('Failed to save order after payment ❌', err);
          }
        });
      });
    } else {
      console.warn('No order data or userId found in localStorage ⚠️');
    }

    // ⏳ Redirection après 10 secondes
    setTimeout(() => {
      this.router.navigate(['/mes-commandes']);
    }, 10000);
  }
}
