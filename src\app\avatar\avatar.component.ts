// src/app/avatar/avatar.component.ts
import { Component, OnInit, OnDestroy } from '@angular/core';
import { TtsWebSocketService } from '../services/tts-websocket.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'avatar',
  templateUrl: './avatar.component.html',
  styleUrls: ['./avatar.component.scss']
})
export class AvatarComponent implements OnInit, OnDestroy {
  public isConnected = false;
  public isPlaying = false;
  public autoPlayEnabled = true; // Par défaut, lecture automatique activée
  public audioQueue = 0; // Nombre d'audios en attente (pour affichage)

  private subscriptions: Subscription[] = [];

  constructor(private ttsService: TtsWebSocketService) {}

  ngOnInit(): void {
    // S'abonner aux événements du service
    this.subscriptions.push(
      // Écouter quand l'audio commence/s'arrête
      this.ttsService.onSpeaking.subscribe(speaking => {
        this.isPlaying = speaking;
        console.log(`🎵 État lecture: ${speaking ? 'EN COURS' : 'ARRÊTÉE'}`);
      }),

      // Écouter quand un audio est prêt
      this.ttsService.onAudioReady.subscribe(() => {
        console.log('🎵 Audio prêt détecté');
      }),

      // Écouter le début de session
      this.ttsService.onSessionStart.subscribe(sessionData => {
        console.log('🚀 Session démarrée dans le composant:', sessionData);
      }),

      // Écouter la fin de session
      this.ttsService.onSessionEnd.subscribe(endData => {
        console.log('🏁 Session terminée dans le composant:', endData);
      })
    );

    // Démarrer automatiquement la connexion avec lecture automatique
    this.startAutoPlaySession();
  }

  ngOnDestroy(): void {
    // Nettoyer les abonnements
    this.subscriptions.forEach(sub => sub.unsubscribe());
    
    // Déconnecter le service
    this.ttsService.disconnect();
  }

  /**
   * Démarre une session avec lecture automatique
   */
  startAutoPlaySession(): void {
    console.log('🚀 Démarrage de la session avec lecture automatique');
    this.ttsService.setAutoPlay(true);
    this.ttsService.connectWithAutoPlay();
    this.isConnected = true;
    this.autoPlayEnabled = true;
  }

  /**
   * Démarre une session manuelle (sans lecture automatique)
   */
  startManualSession(): void {
    console.log('🎯 Démarrage de la session manuelle');
    this.ttsService.setAutoPlay(false);
    this.ttsService.connect();
    this.isConnected = true;
    this.autoPlayEnabled = false;
  }

  /**
   * Lance manuellement la lecture
   */
  playAudio(): void {
    if (!this.isConnected) {
      this.startAutoPlaySession();
    } else {
      this.ttsService.playManually();
    }
  }

  /**
   * Active/désactive la lecture automatique
   */
  toggleAutoPlay(): void {
    this.autoPlayEnabled = !this.autoPlayEnabled;
    this.ttsService.setAutoPlay(this.autoPlayEnabled);
    console.log(`🔄 Lecture automatique ${this.autoPlayEnabled ? 'activée' : 'désactivée'}`);
  }

  /**
   * Redémarre la connexion
   */
  restartSession(): void {
    console.log('🔄 Redémarrage de la session');
    this.ttsService.disconnect();
    this.isConnected = false;
    
    // Redémarrer après un petit délai
    setTimeout(() => {
      this.startAutoPlaySession();
    }, 500);
  }

  /**
   * Arrête la session
   */
  stopSession(): void {
    console.log('🛑 Arrêt de la session');
    this.ttsService.disconnect();
    this.isConnected = false;
  }
}