import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})

export class DashboardComponent {
  isAdmin: boolean = false;
  constructor(private router: Router) {
    const role = localStorage.getItem('role');
    this.isAdmin = role === 'ADMIN';
  }

  sections = [
    { title: 'Utilisateurs', image: 'assets/list-user.png', route: '/users' },
    { title: 'Commandes', image: 'assets/list-orders.jpg', route: '/orders' },
    { title: 'Statistiques', image: 'assets/stats.png', route: '/statistics' }
  ];
  
}
