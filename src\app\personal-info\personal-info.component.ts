import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastNotificationService } from '../services/toast-notification.service';

@Component({
  selector: 'app-personal-info',
  templateUrl: './personal-info.component.html',
  styleUrls: ['./personal-info.component.scss']
})
export class PersonalInfoComponent implements OnInit {
  personalInfoForm!: FormGroup;
  userInfo: any; // pour stocker les données récupérées

  constructor(private fb: FormBuilder,private router: Router,private toastService: ToastNotificationService
  ) {
    const token = localStorage.getItem('jwt');
    if (!token) {
         this.toastService.showError(
          'Session expirée 🔒',
          'Veuillez vous reconnecter pour accéder à vos commandes'
        );      
    
      this.router.navigate(['/']);
    }
  }

  ngOnInit(): void {
    // Récupérer les informations utilisateur stockées dans localStorage
    const storedUserInfo = localStorage.getItem('userInfo');
    if (storedUserInfo) {
      this.userInfo = JSON.parse(storedUserInfo);

      // Créer un formulaire avec les données récupérées
      this.personalInfoForm = this.fb.group({
        address: ['', Validators.required],
        phone: ['', Validators.required]
      });
    } else {
      // Rediriger si les informations de l'utilisateur ne sont pas trouvées
      // this.router.navigate(['/login']);
    }
  }

  onSubmit(): void {
    if (this.personalInfoForm.valid) {
      const { address, phone } = this.personalInfoForm.value;

      // Ajouter les informations personnelles à l'objet utilisateur existant
      const userInfoToUpdate = {
        ...this.userInfo,
        address,
        phoneNumber: phone
      };

      // Appeler une méthode pour enregistrer ces informations sur le backend
      // Enregistrer les données dans la base de données, par exemple via un service UserService

      console.log('Informations personnelles soumises:', userInfoToUpdate);
    }
  }
}
