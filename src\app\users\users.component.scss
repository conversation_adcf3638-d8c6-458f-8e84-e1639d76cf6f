/* --- USERS CONTAINER & TABLE STYLE (garde ton code) --- */
.users-container {
  max-width: 800px;
  margin: auto;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  position: relative; /* ➔ Ajouté pour que la modal reste par-dessus */
  min-height: 80vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

h1 {
  margin-bottom: 15px;
  font-size: 2rem;
  font-weight: bold;
  color: transparent;
  background: linear-gradient(90deg, #007bff, #4aa5d2);
  -webkit-background-clip: text;
  background-clip: text;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

input {
  width: 100%;
  padding: 8px;
  margin-bottom: 15px;
  border: 1px solid #ccc;
  border-radius: 5px;
  color: black;
  background: white;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

th, td {
  padding: 10px;
  border: 1px solid #ddd;
  text-align: center;
  color: black;
}

th {
  background: #007bff;
  color: white;
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 16px;
  margin-bottom: 15px;
  color: #007bff;
  text-align: left;
  width: 100%;
}

.back-link {
  cursor: pointer;
  color: #007bff;
  font-weight: bold;
  text-decoration: none;
  transition: color 0.3s ease;
}

.back-link:hover {
  color: #0056b3;
  text-decoration: underline;
}

/* --- MODAL STYLE AJOUTÉ --- */
.modal {
  position: absolute; /* ➔ Pas fixed */
  top: 100px; /* ➔ Tu peux ajuster si besoin */
  left: 50%;
  transform: translateX(-50%);
  background: #ffffff;
  border: 1px solid #ddd;
  border-radius: 10px;
  width: 400px;
  padding: 20px;
  z-index: 1000; /* ➔ pour être au-dessus du tableau */
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.3s ease;
  margin-top: 10px
}

.modal-content {
  text-align: left;
  color: black;
}

.modal-content h2 {
  margin-top: 0;
  color: #007bff;
}

.modal-content p {
  margin: 10px 0;
}

.modal button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 5px;
  margin-top: 15px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.modal button:hover {
  background-color: #0056b3;
}
.modal-content strong {
  color: #007bff; /* Par exemple, couleur bleue */
}

/* --- ANIMATION LÉGÈRE --- */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

