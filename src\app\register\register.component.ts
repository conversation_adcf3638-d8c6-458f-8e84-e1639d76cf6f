import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AuthService } from '../services/auth.service';
import { Router } from '@angular/router';
import { RegisterRequest } from '../model/RegisterRequest.model';
import { AuthResponse } from '../model/AuthResponse.model';
import { ToastNotificationService } from '../services/toast-notification.service';

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss']
})
export class RegisterComponent {
  fullRegisterForm: FormGroup;
  errorMessage: string = '';

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
            private toastService: ToastNotificationService
    
  ) {
    // Formulaire complet
    this.fullRegisterForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', Validators.required],
      address: ['', Validators.required],
      phoneNumber: ['', Validators.required]  // Utiliser phoneNumber pour plus de clarté
    }, { validators: this.passwordsMatchValidator });
  }

  // Vérifie si les deux mots de passe correspondent
  passwordsMatchValidator(form: FormGroup) {
    const password = form.get('password')?.value;
    const confirmPassword = form.get('confirmPassword')?.value;
    return password === confirmPassword ? null : { mismatch: true };
  }

  onSubmit(): void {
    if (this.fullRegisterForm.invalid) {
      return;
    }

    const { firstName, lastName, email, password, address, phoneNumber } = this.fullRegisterForm.value; // Changement ici : phoneNumber au lieu de phone

    const payload: RegisterRequest = {
      email,
      password,
      displayName: `${firstName} ${lastName}`,
      phoneNumber: phoneNumber, // Utilisation de phoneNumber
      address: address
    };

    this.authService.register(payload).subscribe({
      next: (res: AuthResponse) => {
        // Stockage des infos essentielles
        const userInfo = {
          email: res.email,
          role: res.role,
          address: address,
          phoneNumber: phoneNumber // Utilisation de phoneNumber
        };

        localStorage.setItem('userInfo', JSON.stringify(userInfo));
        localStorage.setItem('jwt', res.jwt); // Stocke bien le jwt

        this.router.navigate(['/']); // Redirection après inscription
      },
     error: err => {
  console.error('Erreur lors de l’inscription', err);

  const message = err.error?.message || 'Échec de l’inscription';
  this.toastService.showError(
    '❌ Erreur lors de l’inscription email deja existe',
    message
  );

  this.errorMessage = message; // Optionnel si tu veux aussi afficher le message ailleurs
}
    });
  }
}
