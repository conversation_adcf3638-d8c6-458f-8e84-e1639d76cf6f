body, html {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: Arial, sans-serif;
  }
  
  .container {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f0f0f0;
  }
  
  .split-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    width: 100%;
    max-width: 1200px;
    height: 100vh;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  }
  
  .image-section {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #000;
    overflow: hidden;
  }
  
  .background-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .form-section {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
  }
  
  .form-container {
    width: 90%;
    max-width: 400px;
    padding: 20px;
  }
  
  .register-form {
    width: 100%;
  }
  
  h2 {
    margin-bottom: 15px;
    font-size: 2rem; /* <PERSON>lle de police plus grande */
    font-weight: bold; /* Texte en gras */
    color: transparent; /* Couleur de texte transparente */
    background: linear-gradient(90deg, #168ddc, #10052b); /* Dégradé de couleur */
    -webkit-background-clip: text; /* Applique le dégradé au texte */
    background-clip: text;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2); /* Ombre portée */
  }
  
  .form-group {
    margin-bottom: 15px;
    text-align: left;
  }
  
  label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
    color: black;
  }
  
  input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    color: black;
    background: white;
  }
  
  button {
    width: 100%;
    padding: 10px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }
  
  button:disabled {
    background: #ccc;
  }
  
  .error {
    color: red;
    font-size: 14px;
    margin-top: 10px;
  }
  .error-message {
  color: red;
  font-size: 0.9em;
  margin-top: 5px;
}

  
  @media (max-width: 768px) {
    .split-layout {
      grid-template-columns: 1fr;
      height: auto;
    }
  
    .image-section {
      height: 300px;
    }
  
    .form-section {
      padding: 20px 0;
    }
  }