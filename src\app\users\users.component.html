<div *ngIf="hasRole('ADMIN'); else accessDenied" class="users-container">

  <div class="breadcrumb">
    <span (click)="goToDashboard()" class="back-link">
      ← Dashboard
    </span>  
    <span> / Liste des utilisateurs</span>
  </div>

  <h1>Liste des Utilisateurs</h1>

  <input type="text" [(ngModel)]="searchTerm" placeholder="Rechercher un utilisateur..." />

  <table>
    <thead>
      <tr>
        <th>ID</th>
        <th>Nom</th>
        <th>Email</th>
        <th>Rôle</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let user of filteredUsers">
        <td>{{ user.uid }}</td>
        <td>{{ user.displayName }}</td>
        <td>{{ user.email }}</td>
        <td>{{ user.role }}</td>
        <td>
          <button class="view-btn" [routerLink]="['/user-details', user.uid]">📋 Consulter</button>
                    <button class="archive-btn1" (click)="onArchiveUser(user.uid)">📁 Archiver</button>
        </td>
      </tr>
    </tbody>
  </table>
  <div *ngIf="selectedUser" class="modal" (click)="closeModal()">
    <div class="modal-content" (click)="$event.stopPropagation()">
      <h2>Détails de l'utilisateur</h2>
      <p><strong>Nom:</strong> {{ selectedUser.displayName }}</p>
      <p><strong>Email:</strong> {{ selectedUser.email }}</p>
      <p><strong>Numéro de téléphone:</strong> {{ selectedUser.phoneNumber }}</p>
      <p><strong>Adresse:</strong> {{ selectedUser.address }}</p>
      <p><strong>Rôle:</strong> {{ selectedUser.role }}</p>
      <p><strong>Statut:</strong> {{ selectedUser.archived ? 'Archivé' : 'Actif' }}</p>
  
      <button class="close-btn" (click)="closeModal()">Fermer</button>
    </div>
  </div>
  
  
  

  <p *ngIf="filteredUsers.length === 0">Aucun utilisateur trouvé.</p>

</div>

<!-- Template si non-admin -->
<ng-template #accessDenied>
  <div class="orders-container">
    <h1>Accès refusé</h1>
    <p>Cette page est réservée aux administrateurs.</p>
  </div>
</ng-template>
